// Offline Debug Panel for Driver Connect PWA
// Provides debugging capabilities for offline QR data storage and sync

import React, { useState, useEffect } from 'react';
import { driverConnectOffline } from '../../services/driverConnectOffline';
import { backgroundSync } from '../../services/backgroundSync';
import { usePWAStatus } from '../../hooks/usePWAStatus';

const OfflineDebugPanel = ({ show = false }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [debugData, setDebugData] = useState({
    queuedConnections: 0,
    storedData: [],
    syncStats: null,
    lastSync: null
  });
  const [loading, setLoading] = useState(false);

  const { isPWA, isOnline, queuedConnections } = usePWAStatus();

  // Update debug data
  const refreshDebugData = async () => {
    try {
      setLoading(true);
      
      const [pendingConnections, syncStats] = await Promise.all([
        driverConnectOffline.getPendingConnections(),
        backgroundSync.getSyncStats()
      ]);

      setDebugData({
        queuedConnections: pendingConnections.length,
        storedData: pendingConnections.slice(0, 5), // Show first 5 for display
        syncStats,
        lastSync: localStorage.getItem('lastBackgroundSync')
      });
    } catch (error) {
      console.error('Failed to refresh debug data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Auto-refresh debug data when panel is expanded
  useEffect(() => {
    if (isExpanded) {
      refreshDebugData();
      const interval = setInterval(refreshDebugData, 5000);
      return () => clearInterval(interval);
    }
  }, [isExpanded]);

  // Inspect stored QR data
  const inspectQRData = async () => {
    try {
      setLoading(true);
      const pendingConnections = await driverConnectOffline.getPendingConnections();
      
      if (pendingConnections.length === 0) {
        alert('No QR data stored offline');
        return;
      }

      const dataDisplay = pendingConnections.map((conn, index) => {
        const driverInfo = conn.apiPayload?.driver_qr_data;
        const truckInfo = conn.apiPayload?.truck_qr_data;
        
        return `Connection ${index + 1}:
Driver ID: ${driverInfo?.employee_id || 'N/A'}
Driver Name: ${driverInfo?.name || 'N/A'}
Truck ID: ${truckInfo?.id || 'N/A'}
Truck Number: ${truckInfo?.number || 'N/A'}
Action: ${conn.action || 'N/A'}
Status: ${conn.status || 'N/A'}
Priority: ${conn.priority || 'N/A'}
Stored: ${new Date(conn.timestamp).toLocaleString()}
---`;
      }).join('\n');

      alert(`Stored QR Data (${pendingConnections.length} connections):\n\n${dataDisplay}`);
    } catch (error) {
      console.error('Failed to inspect QR data:', error);
      alert('Failed to inspect QR data: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Clear QR data
  const clearQRData = async () => {
    if (!confirm('Are you sure you want to clear all stored QR data? This cannot be undone.')) {
      return;
    }

    try {
      setLoading(true);
      await driverConnectOffline.clearAllConnections();
      await refreshDebugData();
      alert('QR data cleared successfully');
    } catch (error) {
      console.error('Failed to clear QR data:', error);
      alert('Failed to clear QR data: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Sync offline data
  const syncOfflineData = async () => {
    if (!isOnline) {
      alert('Cannot sync offline data - no internet connection');
      return;
    }

    try {
      setLoading(true);
      const pendingConnections = await driverConnectOffline.getPendingConnections();
      
      if (pendingConnections.length === 0) {
        alert('No data to sync');
        return;
      }

      const result = await backgroundSync.syncAll();
      await refreshDebugData();
      
      alert(`Sync completed:\nDriver Connections: ${result.driverConnections?.synced || 0} synced, ${result.driverConnections?.failed || 0} failed`);
    } catch (error) {
      console.error('Failed to sync offline data:', error);
      alert('Failed to sync offline data: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Test manual sync (simulation)
  const testManualSync = async () => {
    try {
      setLoading(true);
      const pendingConnections = await driverConnectOffline.getPendingConnections();
      
      if (pendingConnections.length === 0) {
        alert('No data to sync');
        return;
      }

      if (!isOnline) {
        alert('Cannot perform sync test - no internet connection');
        return;
      }

      alert(`Manual Sync Test:\n${pendingConnections.length} connections would be synced\n\nBreakdown:\n${pendingConnections.map((conn, i) => `${i + 1}. ${conn.action} (${conn.priority} priority)`).join('\n')}`);
    } catch (error) {
      console.error('Failed to test manual sync:', error);
      alert('Failed to test manual sync: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  if (!show || !isPWA) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className={`bg-gray-800 text-white rounded-lg shadow-lg transition-all duration-300 ${
        isExpanded ? 'w-96 max-h-96 overflow-y-auto' : 'w-auto'
      }`}>
        {/* Header */}
        <div 
          className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-700"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
            <span className="text-sm font-medium">Debug Panel</span>
            <span className="text-xs bg-gray-600 px-2 py-1 rounded">
              {queuedConnections} queued
            </span>
          </div>
          <div className="text-gray-400">
            {isExpanded ? '−' : '+'}
          </div>
        </div>

        {/* Expanded Content */}
        {isExpanded && (
          <div className="p-3 border-t border-gray-600 space-y-3">
            {/* Status Info */}
            <div className="text-xs space-y-1">
              <div>PWA Mode: {isPWA ? '✅' : '❌'}</div>
              <div>Online: {isOnline ? '✅' : '❌'}</div>
              <div>Queued: {debugData.queuedConnections}</div>
              {debugData.lastSync && (
                <div>Last Sync: {new Date(debugData.lastSync).toLocaleTimeString()}</div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="space-y-2">
              <button
                onClick={inspectQRData}
                disabled={loading}
                className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white text-xs py-2 px-3 rounded transition-colors"
              >
                {loading ? 'Loading...' : 'Inspect Stored QR Data'}
              </button>
              
              <button
                onClick={clearQRData}
                disabled={loading}
                className="w-full bg-red-600 hover:bg-red-700 disabled:bg-gray-600 text-white text-xs py-2 px-3 rounded transition-colors"
              >
                Clear QR Data
              </button>
              
              <div className="flex space-x-2">
                <button
                  onClick={syncOfflineData}
                  disabled={loading || !isOnline}
                  className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white text-xs py-2 px-3 rounded transition-colors"
                >
                  Sync Offline Data
                </button>
                
                <button
                  onClick={testManualSync}
                  disabled={loading}
                  className="flex-1 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 text-white text-xs py-2 px-3 rounded transition-colors"
                >
                  Test Manual Sync
                </button>
              </div>
            </div>

            {/* Quick Stats */}
            {debugData.syncStats && (
              <div className="text-xs text-gray-400 pt-2 border-t border-gray-600">
                <div>Sync Status: {debugData.syncStats.syncInProgress ? 'In Progress' : 'Idle'}</div>
                {debugData.syncStats.driverConnections && (
                  <div>
                    Connections: {debugData.syncStats.driverConnections.pending} pending, 
                    {debugData.syncStats.driverConnections.failed} failed
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
      
      <div className="mt-2 pt-2 border-t border-gray-600 text-xs text-gray-400">
        Use browser dev tools → Application → IndexedDB → HaulingQROffline → connectionQueue to inspect data
      </div>
    </div>
  );
};

export default OfflineDebugPanel;