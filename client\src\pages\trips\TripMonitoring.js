import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { tripsAPI, assignmentsAPI, api } from '../../services/api';
import TripsTable from './components/TripsTable';
import TripDetailModal from './components/TripDetailModal';
import DurationMetrics from './components/DurationMetrics';
import DateRangeFilter from './components/DateRangeFilter';
import AssignmentFilter from './components/AssignmentFilter';
import StatusFilter from './components/StatusFilter';
import DriverFilter from './components/DriverFilter';
import RouteFilter from './components/RouteFilter';
import toast from 'react-hot-toast';


const TripMonitoring = () => {
  const [trips, setTrips] = useState([]);
  const [activeAssignments, setActiveAssignments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 10,
    hasNextPage: false,
    hasPrevPage: false
  });

  // Modal states
  const [showTripDetailModal, setShowTripDetailModal] = useState(false);
  const [selectedTrip, setSelectedTrip] = useState(null);

  // Filter and search states
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    assignment_id: '',
    date_from: '',
    date_to: '',
    driver_name: '',
    route: '',
    sortBy: 'created_at',
    sortOrder: 'desc'
  });

  // New state for comprehensive filter options
  const [filterOptions, setFilterOptions] = useState({
    statuses: [],
    assignments: [],
    drivers: [],
    routes: [],
    loading: false
  });

  // Advanced filter view toggle
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // Auto-refresh interval
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds

  // Memoize filters to prevent infinite re-renders
  // Using object reference stability approach - only re-compute when filter values actually change
  const memoizedFilters = useMemo(() => {
    // Return a new object only when filter values actually change
    return {
      search: filters.search,
      status: filters.status,
      assignment_id: filters.assignment_id,
      date_from: filters.date_from,
      date_to: filters.date_to
    };
  }, [
    filters.search,
    filters.status,
    filters.assignment_id,
    filters.date_from,
    filters.date_to
  ]);

  // Load trips with error handling
  const loadTrips = useCallback(async (page = 1) => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: pagination.itemsPerPage,
        ...memoizedFilters
      };

      const response = await tripsAPI.getAll({ params });
      setTrips(response.data.data);
      setPagination(response.data.pagination);
    } catch (error) {
      console.error('Error loading trips:', error);

      // Better error handling
      if (error.code === 'ERR_NETWORK') {
        toast.error('Network error. Please check your connection.');
      } else if (error.response?.status === 429) {
        toast.error('Too many requests. Please wait a moment.');
      } else {
        toast.error(error.response?.data?.message || 'Failed to load trips');
      }
    } finally {
      setLoading(false);
    }
  }, [pagination.itemsPerPage, memoizedFilters]);

  // Load comprehensive filter options
  const loadFilterOptions = useCallback(async () => {
    setFilterOptions(prev => ({ ...prev, loading: true }));
    try {
      // Load all assignments (not just active) for comprehensive filtering
      const assignmentsResponse = await assignmentsAPI.getAll({ params: { limit: 1000, sortBy: 'created_at', sortOrder: 'desc' } });

      // Load filter options from trips API (statuses, drivers, routes)
      const filterOptionsResponse = await api.get('/trips/filter-options');

      const assignments = assignmentsResponse.data.data || [];
      const filterData = filterOptionsResponse.data.data || {};
      
      // Define comprehensive trip statuses based on the current system
      const tripStatuses = [
        { value: 'assigned', label: 'Assigned', description: 'Trip assigned to driver' },
        { value: 'loading_start', label: 'Loading Started', description: 'Loading in progress' },
        { value: 'loading_end', label: 'Loading Completed', description: 'Loading finished, traveling' },
        { value: 'unloading_start', label: 'Unloading Started', description: 'Unloading in progress' },
        { value: 'unloading_end', label: 'Unloading Completed', description: 'Unloading finished, returning' },
        { value: 'trip_completed', label: 'Trip Completed', description: 'Fully completed trip' },
        { value: 'auto_assignment', label: 'Auto Assignment', description: 'Automatically assigned by system' },
        { value: 'dynamic_route', label: 'Dynamic Route', description: 'Progressive route discovery in progress' },
        { value: 'cancelled', label: 'Cancelled', description: 'Cancelled or rejected' }
      ];

      // Group assignments by categories
      const groupedAssignments = assignments.reduce((acc, assignment) => {
        // Group by truck
        const truckKey = `truck_${assignment.truck_id}`;
        if (!acc.trucks[truckKey]) {
          acc.trucks[truckKey] = {
            label: `${assignment.truck_number} (${assignment.license_plate})`,
            assignments: []
          };
        }
        acc.trucks[truckKey].assignments.push(assignment);

        // Group by driver
        const driverKey = `driver_${assignment.driver_id}`;
        if (!acc.drivers[driverKey]) {
          acc.drivers[driverKey] = {
            label: `${assignment.driver_name} (${assignment.employee_id})`,
            assignments: []
          };
        }
        acc.drivers[driverKey].assignments.push(assignment);

        // Group by route (loading -> unloading)
        const routeKey = `route_${assignment.loading_location_id}_${assignment.unloading_location_id}`;
        if (!acc.routes[routeKey]) {
          acc.routes[routeKey] = {
            label: `${assignment.loading_location_name} → ${assignment.unloading_location_name}`,
            assignments: []
          };
        }
        acc.routes[routeKey].assignments.push(assignment);

        // Group by priority
        if (!acc.priorities[assignment.priority]) {
          acc.priorities[assignment.priority] = {
            label: assignment.priority.charAt(0).toUpperCase() + assignment.priority.slice(1),
            assignments: []
          };
        }
        acc.priorities[assignment.priority].assignments.push(assignment);

        // Group by status
        if (!acc.statuses[assignment.status]) {
          acc.statuses[assignment.status] = {
            label: assignment.status.charAt(0).toUpperCase() + assignment.status.slice(1).replace('_', ' '),
            assignments: []
          };
        }
        acc.statuses[assignment.status].assignments.push(assignment);

        return acc;
      }, {
        trucks: {},
        drivers: {},
        routes: {},
        priorities: {},
        statuses: {}
      });

      setFilterOptions({
        statuses: filterData.statuses || tripStatuses,
        assignments: assignments,
        drivers: filterData.drivers || [],
        routes: filterData.routes || [],
        groupedAssignments,
        loading: false
      });

      // Update legacy activeAssignments for backward compatibility
      setActiveAssignments(assignments.filter(a => ['assigned', 'in_progress'].includes(a.status)));
    } catch (error) {
      console.error('Error loading filter options:', error);
      setFilterOptions(prev => ({ ...prev, loading: false }));
      // Fallback to basic statuses
      setFilterOptions(prev => ({
        ...prev,
        statuses: [
          { value: 'assigned', label: 'Assigned' },
          { value: 'loading_start', label: 'Loading Started' },
          { value: 'loading_end', label: 'Loading Completed' },
          { value: 'unloading_start', label: 'Unloading Started' },
          { value: 'unloading_end', label: 'Unloading Completed' },
          { value: 'trip_completed', label: 'Trip Completed' },
          { value: 'auto_assignment', label: 'Auto Assignment' },
          { value: 'dynamic_route', label: 'Dynamic Route' },
          { value: 'cancelled', label: 'Cancelled' }
        ],
        loading: false
      }));
    }
  }, []);

  // Load active assignments for filter dropdown (legacy function)
  // (Removed) Load active assignments for filter dropdown (legacy function)
  // const loadActiveAssignments = useCallback(async () => {
  //   try {
  //     const response = await assignmentsAPI.getActive();
  //     setActiveAssignments(response.data.data || []);
  //   } catch (error) {
  //     // Error loading active assignments
  //   }
  // }, []);
  // Remove debounced function - using proven pattern from Drivers/Trucks pages

  // Auto-refresh functionality
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(() => {
        loadTrips(pagination.currentPage);
      }, refreshInterval * 1000);

      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, loadTrips, pagination.currentPage]);

  // Date range validation
  const validateDateRange = useCallback((startDate, endDate) => {
    if (!startDate || !endDate) return { isValid: true, error: null };
    
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (end < start) {
      return {
        isValid: false,
        error: 'End date must be after start date'
      };
    }
    
    // Check if date range is too large (optional - can be adjusted)
    const daysDiff = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
    if (daysDiff > 365) {
      return {
        isValid: false,
        error: 'Date range cannot exceed 365 days'
      };
    }
    
    return { isValid: true, error: null };
  }, []);

  // Enhanced filter change handler with date validation
  const handleFilterChangeWithValidation = useCallback((newFilters) => {
    const updatedFilters = { ...filters, ...newFilters };
    
    // Validate date range if both dates are present
    if (updatedFilters.date_from && updatedFilters.date_to) {
      const validation = validateDateRange(updatedFilters.date_from, updatedFilters.date_to);
      if (!validation.isValid) {
        toast.error(validation.error);
        return;
      }
    }
    
    setFilters(updatedFilters);
  }, [filters, validateDateRange]);

  // Initial load
  useEffect(() => {
    loadTrips();
    loadFilterOptions(); // Load comprehensive filter options
  }, [loadTrips, loadFilterOptions]);

  // Handle page change
  const handlePageChange = (page) => {
    loadTrips(page);
  };

  // Handle filter changes
  const handleFilterChange = (newFilters) => {
    handleFilterChangeWithValidation(newFilters);
  };

  // Handle sorting
  const handleSort = (column) => {
    const newSortOrder = filters.sortBy === column && filters.sortOrder === 'asc' ? 'desc' : 'asc';
    handleFilterChange({
      sortBy: column,
      sortOrder: newSortOrder
    });
  };

  // Handle search - use proven pattern from Drivers/Trucks pages
  const handleSearch = (searchTerm) => {
    setFilters(prev => ({ ...prev, search: searchTerm }));
  };

  // Clear all filters - use proven pattern
  const clearFilters = () => {
    setFilters({
      search: '',
      status: '',
      assignment_id: '',
      date_from: '',
      date_to: '',
      driver_name: '',
      route: '',
      sortBy: 'created_at',
      sortOrder: 'desc'
    });
  };

  // Handle trip detail view
  const handleViewTrip = (trip) => {
    setSelectedTrip(trip);
    setShowTripDetailModal(true);
  };

  // Handle manual refresh
  const handleRefresh = () => {
    loadTrips(pagination.currentPage);
    toast.success('Trips refreshed');
  };

  // Count active filters
  const activeFiltersCount = Object.entries(filters).filter(([key, value]) => 
    key !== 'sortBy' && key !== 'sortOrder' && value !== ''
  ).length;

  // Enhanced trip statistics with dynamic route discovery tracking
  const tripStats = useMemo(() => {
    // Helper function to check if assignment is dynamic
    const isDynamicAssignment = (trip) => {
      try {
        if (!trip.assignment_notes) return false;
        const notes = typeof trip.assignment_notes === 'string'
          ? JSON.parse(trip.assignment_notes)
          : trip.assignment_notes;
        return notes.creation_method === 'dynamic_assignment';
      } catch {
        return false;
      }
    };

    // CORRECTED FIX: Helper function to check if dynamic route discovery is actively in progress
    const isActiveDiscovery = (trip) => {
      if (!isDynamicAssignment(trip)) return false;

      // CORRECTED LOGIC: Check if assignment exists in Assignment Management with established locations
      const hasEstablishedLoadingLocation = trip.loading_location_id !== null && trip.loading_location_id !== undefined;
      const hasEstablishedUnloadingLocation = trip.unloading_location_id !== null && trip.unloading_location_id !== undefined;
      const existsInAssignmentManagement = hasEstablishedLoadingLocation && hasEstablishedUnloadingLocation;

      // If assignment exists in Assignment Management, NEVER show Dynamic Route
      if (existsInAssignmentManagement) {
        return false;
      }

      // Only show Dynamic Route for genuinely new assignments without established locations
      return true;
    };

    const stats = {
      total: trips.length,
      loading: trips.filter(t => ['loading_start'].includes(t.status)).length,
      unloading: trips.filter(t => ['unloading_start'].includes(t.status)).length,
      traveling: trips.filter(t => ['loading_end','unloading_end'].includes(t.status)).length,
      completed: trips.filter(t => t.status === 'trip_completed').length,
      cancelled: trips.filter(t => t.status === 'cancelled').length,
      // FIXED: Dynamic route discovery stats - only count active discovery, not all dynamic assignments
      dynamicRoutes: trips.filter(t => isDynamicAssignment(t)).length, // Total dynamic assignments (for reference)
      routeDiscovery: trips.filter(t => isActiveDiscovery(t)).length // Only actively discovering routes
    };
    stats.active = stats.loading + stats.unloading + stats.traveling;
    return stats;
  }, [trips]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Trip Monitoring</h1>
          <p className="text-secondary-600 mt-1">Real-time tracking of hauling trips</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          {/* Auto-refresh toggle */}
          <div className="flex items-center space-x-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
              />
              <span className="ml-2 text-sm text-secondary-700">Auto-refresh</span>
            </label>
            {autoRefresh && (
              <select
                value={refreshInterval}
                onChange={(e) => setRefreshInterval(parseInt(e.target.value))}
                className="text-sm border-secondary-300 rounded-md"
              >
                <option value={10}>10s</option>
                <option value={30}>30s</option>
                <option value={60}>1m</option>
                <option value={300}>5m</option>
              </select>
            )}
          </div>
          
          <button
            onClick={handleRefresh}
            className="btn btn-secondary"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Refresh
          </button>
        </div>
      </div>

      {/* Trip Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-7 gap-4">
        <div className="bg-white p-4 rounded-lg shadow-sm border border-secondary-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-medium text-sm">{tripStats.active}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Active</p>
              <p className="text-xs text-secondary-500">In progress</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-secondary-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <span className="text-green-600 font-medium text-sm">{tripStats.loading}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Loading</p>
              <p className="text-xs text-secondary-500">At source</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-secondary-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <span className="text-yellow-600 font-medium text-sm">{tripStats.traveling}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Traveling</p>
              <p className="text-xs text-secondary-500">En route</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-secondary-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <span className="text-purple-600 font-medium text-sm">{tripStats.unloading}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Unloading</p>
              <p className="text-xs text-secondary-500">At destination</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-secondary-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <span className="text-green-600 font-medium text-sm">{tripStats.completed}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Completed</p>
              <p className="text-xs text-secondary-500">Finished</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-secondary-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-medium text-sm">{tripStats.dynamicRoutes}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Dynamic Routes</p>
              <p className="text-xs text-secondary-500">Progressive discovery</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-secondary-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                <span className="text-indigo-600 font-medium text-sm">{tripStats.routeDiscovery}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Route Discovery</p>
              <p className="text-xs text-secondary-500">Active learning</p>
            </div>
          </div>
        </div>

        {/* Cancelled Trips Card */}
        <div className="bg-white p-4 rounded-lg shadow-sm border border-secondary-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                <span className="text-gray-600 font-medium text-sm">{tripStats.cancelled}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Cancelled</p>
              <p className="text-xs text-secondary-500">Rejected exceptions</p>
            </div>
          </div>
        </div>
      </div>

      {/* Duration Metrics */}
      <DurationMetrics trips={trips} loading={loading} />

      {/* Enhanced Search and Filter System */}
      <div className="bg-white rounded-lg shadow-sm border border-secondary-200">
        {/* Filter Header */}
        <div className="flex items-center justify-between p-4 border-b border-secondary-200">
          <div className="flex items-center space-x-3">
            <h3 className="text-lg font-medium text-secondary-900">Search & Filters</h3>
            {activeFiltersCount > 0 && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                {activeFiltersCount} active
              </span>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-secondary-700 bg-secondary-100 rounded-md hover:bg-secondary-200 transition-colors"
            >
              {showAdvancedFilters ? (
                <>
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  Simple View
                </>
              ) : (
                <>
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                  </svg>
                  Advanced Filters
                </>
              )}
            </button>
            <button
              onClick={clearFilters}
              disabled={activeFiltersCount === 0}
              className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-red-700 bg-red-100 rounded-md hover:bg-red-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              Clear All
            </button>
          </div>
        </div>

        <div className="p-4">
          {!showAdvancedFilters ? (
            /* Simple Filter View */
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Search */}
              <div>
                <label htmlFor="search" className="block text-sm font-medium text-secondary-700 mb-1">
                  Search
                </label>
                <input
                  type="text"
                  id="search"
                  value={filters.search}
                  onChange={(e) => handleSearch(e.target.value)}
                  placeholder="Search trips..."
                  className="input"
                />
              </div>

              {/* Simple Status Filter */}
              <div>
                <label htmlFor="status_simple" className="block text-sm font-medium text-secondary-700 mb-1">
                  Status
                </label>
                <select
                  id="status_simple"
                  value={filters.status}
                  onChange={(e) => handleFilterChange({ status: e.target.value })}
                  className="input"
                  disabled={filterOptions.loading}
                >
                  <option value="">All Status</option>
                  {filterOptions.statuses.map((status) => (
                    <option key={status.value} value={status.value}>
                      {status.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Simple Assignment Filter */}
              <div>
                <label htmlFor="assignment_simple" className="block text-sm font-medium text-secondary-700 mb-1">
                  Assignment
                </label>
                <select
                  id="assignment_simple"
                  value={filters.assignment_id}
                  onChange={(e) => handleFilterChange({ assignment_id: e.target.value })}
                  className="input"
                  disabled={filterOptions.loading}
                >
                  <option value="">All Assignments</option>
                  {filterOptions.assignments.slice(0, 20).map((assignment) => (
                    <option key={assignment.id} value={assignment.id}>
                      {assignment.truck_number} - {assignment.driver_name}
                    </option>
                  ))}
                  {filterOptions.assignments.length > 20 && (
                    <option disabled>... and {filterOptions.assignments.length - 20} more (use Advanced Filters)</option>
                  )}
                </select>
              </div>

              {/* Simple Date Range */}
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-1">
                  Date Range (Inclusive)
                </label>
                <div className="grid grid-cols-2 gap-1">
                  <input
                    type="date"
                    value={filters.date_from}
                    onChange={(e) => handleFilterChange({ date_from: e.target.value })}
                    className="input text-xs"
                    placeholder="Start"
                    title="Start date (inclusive)"
                  />
                  <input
                    type="date"
                    value={filters.date_to}
                    onChange={(e) => handleFilterChange({ date_to: e.target.value })}
                    className="input text-xs"
                    placeholder="End"
                    title="End date (inclusive - includes entire day)"
                  />
                </div>
                {filters.date_from && filters.date_to && (
                  <div className="text-xs text-secondary-500 mt-1">
                    {Math.ceil((new Date(filters.date_to) - new Date(filters.date_from)) / (1000 * 60 * 60 * 24)) + 1} days (inclusive)
                  </div>
                )}
              </div>
            </div>
          ) : (
            /* Advanced Filter View */
            <div className="space-y-6">
              {/* Search */}
              <div>
                <label htmlFor="search_advanced" className="block text-sm font-medium text-secondary-700 mb-2">
                  Search
                </label>
                <input
                  type="text"
                  id="search_advanced"
                  value={filters.search}
                  onChange={(e) => handleSearch(e.target.value)}
                  placeholder="Search by trip number, truck, driver, location, or notes..."
                  className="input"
                />
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-3 xl:grid-cols-5 gap-6">
                {/* Advanced Status Filter */}
                <div>
                  <StatusFilter
                    statuses={filterOptions.statuses}
                    selectedStatus={filters.status}
                    onStatusChange={(status) => handleFilterChange({ status })}
                    loading={filterOptions.loading}
                    showCounts={true}
                  />
                </div>

                {/* Advanced Assignment Filter */}
                <div>
                  <AssignmentFilter
                    assignments={filterOptions.assignments}
                    selectedAssignmentId={filters.assignment_id}
                    onAssignmentChange={(assignmentId) => handleFilterChange({ assignment_id: assignmentId })}
                    loading={filterOptions.loading}
                  />
                </div>

                {/* Driver Filter */}
                <div>
                  <DriverFilter
                    drivers={filterOptions.drivers}
                    selectedDriverName={filters.driver_name}
                    onDriverChange={(driverName) => handleFilterChange({ driver_name: driverName })}
                    loading={filterOptions.loading}
                  />
                </div>

                {/* Route Filter */}
                <div>
                  <RouteFilter
                    routes={filterOptions.routes}
                    selectedRoute={filters.route}
                    onRouteChange={(route) => handleFilterChange({ route })}
                    loading={filterOptions.loading}
                  />
                </div>

                {/* Advanced Date Range Filter */}
                <div>
                  <DateRangeFilter
                    startDate={filters.date_from}
                    endDate={filters.date_to}
                    onDateChange={(dateData) => handleFilterChange(dateData)}
                    showPresets={true}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Filter Summary Tags */}
          {activeFiltersCount > 0 && (
            <div className="mt-4 pt-4 border-t border-secondary-200">
              <div className="flex flex-wrap gap-2">
                {filters.search && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                    🔍 "{filters.search}"
                    <button
                      onClick={() => handleFilterChange({ search: '' })}
                      className="ml-1 text-primary-600 hover:text-primary-800"
                    >
                      ×
                    </button>
                  </span>
                )}
                {filters.status && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    📊 {filterOptions.statuses.find(s => s.value === filters.status)?.label || filters.status}
                    <button
                      onClick={() => handleFilterChange({ status: '' })}
                      className="ml-1 text-green-600 hover:text-green-800"
                    >
                      ×
                    </button>
                  </span>
                )}
                {filters.assignment_id && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                    🚚 {filterOptions.assignments.find(a => a.id.toString() === filters.assignment_id)?.truck_number ||
                         activeAssignments.find(a => a.id.toString() === filters.assignment_id)?.truck_number || 'Assignment'}
                    <button
                      onClick={() => handleFilterChange({ assignment_id: '' })}
                      className="ml-1 text-purple-600 hover:text-purple-800"
                    >
                      ×
                    </button>
                  </span>
                )}
                {filters.driver_name && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    👤 {filters.driver_name}
                    <button
                      onClick={() => handleFilterChange({ driver_name: '' })}
                      className="ml-1 text-yellow-600 hover:text-yellow-800"
                    >
                      ×
                    </button>
                  </span>
                )}
                {filters.route && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                    🛣️ {filters.route}
                    <button
                      onClick={() => handleFilterChange({ route: '' })}
                      className="ml-1 text-indigo-600 hover:text-indigo-800"
                    >
                      ×
                    </button>
                  </span>
                )}
                {(filters.date_from || filters.date_to) && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    📅 {filters.date_from || '...'} → {filters.date_to || '...'}
                    <button
                      onClick={() => handleFilterChange({ date_from: '', date_to: '' })}
                      className="ml-1 text-blue-600 hover:text-blue-800"
                    >
                      ×
                    </button>
                  </span>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Trips Table */}
      <TripsTable
        trips={trips}
        loading={loading}
        pagination={pagination}
        filters={filters}
        onPageChange={handlePageChange}
        onSort={handleSort}
        onViewTrip={handleViewTrip}
        onTripUpdate={() => loadTrips(pagination.currentPage)}
      />

      {/* Trip Detail Modal */}
      {showTripDetailModal && selectedTrip && (
        <TripDetailModal
          trip={selectedTrip}
          onClose={() => {
            setShowTripDetailModal(false);
            setSelectedTrip(null);
          }}
          onRefresh={() => loadTrips(pagination.currentPage)}
        />
      )}
    </div>
  );
};

export default TripMonitoring;