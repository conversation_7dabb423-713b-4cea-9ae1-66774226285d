<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Debug Test</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            padding: 20px; 
            background-color: #f8f9fa;
            line-height: 1.6;
        }
        .test-section { 
            margin: 20px 0; 
            padding: 20px; 
            border: 1px solid #dee2e6; 
            border-radius: 8px;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section h2 {
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        .success { 
            color: #28a745; 
            font-weight: 500;
        }
        .error { 
            color: #dc3545; 
            font-weight: 500;
        }
        .info { 
            color: #17a2b8; 
            font-weight: 500;
        }
        .warning {
            color: #ffc107;
            font-weight: 500;
        }
        
        /* Enhanced <PERSON><PERSON> Styles */
        button { 
            padding: 12px 20px; 
            margin: 8px 5px; 
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            min-width: 140px;
        }
        
        /* Primary Action Buttons */
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
            transform: translateY(-1px);
        }
        
        /* Success Action Buttons */
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-success:hover {
            background-color: #1e7e34;
            transform: translateY(-1px);
        }
        
        /* Warning Action Buttons */
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background-color: #e0a800;
            transform: translateY(-1px);
        }
        
        /* Danger Action Buttons */
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
            transform: translateY(-1px);
        }
        
        /* Secondary Action Buttons */
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #545b62;
            transform: translateY(-1px);
        }
        
        /* Info Action Buttons */
        .btn-info {
            background-color: #17a2b8;
            color: white;
        }
        .btn-info:hover {
            background-color: #138496;
            transform: translateY(-1px);
        }
        
        /* Button Loading State */
        .btn-loading {
            opacity: 0.7;
            cursor: not-allowed;
            pointer-events: none;
        }
        .btn-loading::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            margin: auto;
            border: 2px solid transparent;
            border-top-color: currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        
        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
        
        /* Button Groups */
        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        
        /* Status Indicators */
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            vertical-align: middle;
        }
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        .status-pending { background-color: #ffc107; }
        .status-unknown { background-color: #6c757d; }
        
        /* Enhanced Pre Styles */
        pre { 
            background: #f8f9fa; 
            padding: 15px; 
            overflow-x: auto; 
            border-radius: 6px;
            border: 1px solid #e9ecef;
            font-size: 13px;
            line-height: 1.4;
        }
        
        /* Progress Bars */
        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
        }
        
        /* Enhanced Info Boxes */
        .info-box {
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            border-left: 4px solid;
        }
        .info-box.success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .info-box.error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .info-box.warning {
            background-color: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .info-box.info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        
        /* Instructions Panel */
        .instructions-panel {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .instructions-panel h3 {
            margin-top: 0;
            color: white;
        }
        .instructions-panel ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .instructions-panel li {
            margin: 8px 0;
        }
        
        /* Badge Styles */
        .badge {
            display: inline-block;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
            border-radius: 4px;
            text-transform: uppercase;
        }
        .badge-success {
            background-color: #28a745;
            color: white;
        }
        .badge-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .badge-danger {
            background-color: #dc3545;
            color: white;
        }
        .badge-secondary {
            background-color: #6c757d;
            color: white;
        }
        .badge-info {
            background-color: #17a2b8;
            color: white;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .button-group {
                flex-direction: column;
            }
            button {
                width: 100%;
                margin: 5px 0;
            }
        }
    </style>
</head>
<body>
    <h1>PWA Debug Test</h1>
    
    <div class="instructions-panel">
        <h3>🧪 PWA Offline QR Debug Testing Guide</h3>
        <p>This debug page helps you test and verify PWA offline functionality and QR data storage. Follow these steps for comprehensive testing:</p>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0;">
            <div>
                <h4>🔍 Basic Testing Steps:</h4>
                <ol>
                    <li><strong>Environment Check:</strong> Verify PWA mode detection</li>
                    <li><strong>Service Worker:</strong> Confirm SW is registered and active</li>
                    <li><strong>Cache Status:</strong> Check if offline content is cached</li>
                    <li><strong>IndexedDB:</strong> Verify database accessibility</li>
                </ol>
            </div>
            <div>
                <h4>📱 QR Storage Testing:</h4>
                <ol>
                    <li><strong>Inspect:</strong> View stored QR connections</li>
                    <li><strong>Simulate:</strong> Create test QR data</li>
                    <li><strong>Sync Test:</strong> Test manual sync process</li>
                    <li><strong>Real Sync:</strong> Send data to server API</li>
                </ol>
            </div>
        </div>
        
        <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 5px; margin: 10px 0;">
            <strong>⚠️ Important Notes:</strong>
            <ul style="margin: 5px 0; padding-left: 20px;">
                <li><strong>PWA Mode:</strong> Some features only work when installed as PWA</li>
                <li><strong>Network State:</strong> Test both online and offline scenarios</li>
                <li><strong>Real Sync:</strong> "Sync Offline Data" makes actual API calls</li>
                <li><strong>Browser Console:</strong> Check for detailed logs and errors</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2>1. Environment Detection</h2>
        <div id="env-info"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Service Worker Status</h2>
        <div id="sw-info"></div>
        <div class="button-group">
            <button class="btn-primary" onclick="checkServiceWorker()">
                <span class="status-indicator status-unknown"></span>
                Check Service Worker
            </button>
        </div>
    </div>
    
    <div class="test-section">
        <h2>3. Cache Status</h2>
        <div id="cache-info"></div>
        <div class="button-group">
            <button class="btn-primary" onclick="checkCaches()">
                <span class="status-indicator status-unknown"></span>
                Check Caches
            </button>
        </div>
    </div>
    
    <div class="test-section">
        <h2>4. PWA Mode Detection</h2>
        <div id="pwa-info"></div>
        <div class="button-group">
            <button class="btn-info" onclick="testPWAMode()">
                <span class="status-indicator status-unknown"></span>
                Test PWA Mode
            </button>
        </div>
    </div>
    
    <div class="test-section">
        <h2>5. PWA Status Hook Communication Test</h2>
        <div id="pwa-hook-info"></div>
        <div class="button-group">
            <button class="btn-info" onclick="testPWAStatusHook()">
                <span class="status-indicator status-unknown"></span>
                Test PWA Status Hook Communication
            </button>
        </div>
    </div>
    
    <div class="test-section">
        <h2>6. Navigation Test</h2>
        <div class="button-group">
            <button class="btn-primary" onclick="testNavigation('/driver-connect')">
                <span class="status-indicator status-unknown"></span>
                Test Driver Connect
            </button>
            <button class="btn-primary" onclick="testNavigation('/trip-scanner')">
                <span class="status-indicator status-unknown"></span>
                Test Trip Scanner
            </button>
        </div>
        <div id="nav-info"></div>
    </div>
    
    <div class="test-section">
        <h2>7. IndexedDB Storage Test</h2>
        <div class="button-group">
            <button class="btn-info" onclick="checkIndexedDB()">
                <span class="status-indicator status-unknown"></span>
                Check IndexedDB
            </button>
            <button class="btn-secondary" onclick="checkOfflineData()">
                <span class="status-indicator status-unknown"></span>
                Check Offline QR Data
            </button>
            <button class="btn-danger" onclick="clearOfflineData()">
                <span class="status-indicator status-offline"></span>
                Clear Offline Data
            </button>
        </div>
        <div id="indexeddb-info"></div>
    </div>
    
    <div class="test-section">
        <h2>8. QR Data Storage Inspection & Management</h2>
        <div class="instructions-panel">
            <h3>📱 QR Storage Testing Instructions</h3>
            <ul>
                <li><strong>Inspect:</strong> View all stored QR connections with detailed information</li>
                <li><strong>Clear:</strong> Remove all stored QR data (useful for testing fresh scenarios)</li>
                <li><strong>Simulate:</strong> Create test QR data to verify storage functionality</li>
                <li><strong>Sync:</strong> Send stored data to server or test sync process</li>
            </ul>
            <p><strong>💡 Tip:</strong> Use these tools to verify offline QR scanning works correctly</p>
        </div>
        
        <div class="button-group">
            <button class="btn-info" onclick="inspectQRData()">
                <span class="status-indicator status-unknown"></span>
                Inspect Stored QR Data
            </button>
            <button class="btn-danger" onclick="clearQRData()">
                <span class="status-indicator status-offline"></span>
                Clear QR Data
            </button>
        </div>
        
        <div id="qr-inspection-info"></div>
    </div>
    
    <div class="test-section">
        <h2>9. QR Code Storage Simulation & Sync Testing</h2>
        <div class="instructions-panel">
            <h3>🔄 Sync Testing Instructions</h3>
            <ul>
                <li><strong>Simulate Scans:</strong> Create test QR data to populate offline storage</li>
                <li><strong>Test Manual Sync:</strong> Simulate sync process without sending data to server</li>
                <li><strong>Sync Offline Data:</strong> Actually send stored data to server API endpoint</li>
                <li><strong>Online/Offline:</strong> Test both network states to verify proper behavior</li>
            </ul>
            <p><strong>⚠️ Note:</strong> "Sync Offline Data" makes real API calls - use carefully</p>
        </div>
        
        <div class="button-group">
            <button class="btn-secondary" onclick="simulateDriverScan()">
                <span class="status-indicator status-pending"></span>
                Simulate Driver QR Scan
            </button>
            <button class="btn-secondary" onclick="simulateTruckScan()">
                <span class="status-indicator status-pending"></span>
                Simulate Truck QR Scan
            </button>
        </div>
        
        <div class="button-group">
            <button class="btn-warning" onclick="testManualSync()">
                <span class="status-indicator status-unknown"></span>
                Test Manual Sync
            </button>
            <button class="btn-success" onclick="syncOfflineData()">
                <span class="status-indicator status-online"></span>
                Sync Offline Data
            </button>
        </div>
        
        <div id="qr-simulation-info"></div>
    </div>
    
    <div class="test-section">
        <h2>10. Console Logs</h2>
        <div id="console-logs"></div>
        <div class="button-group">
            <button class="btn-warning" onclick="clearLogs()">
                <span class="status-indicator status-pending"></span>
                Clear Logs
            </button>
        </div>
    </div>

    <script>
        let logs = [];
        
        // Enhanced logging system with different log levels
        const logLevels = {
            ERROR: 'error',
            WARN: 'warn',
            INFO: 'info',
            DEBUG: 'debug'
        };
        
        // Enhanced logging function with categorization
        function enhancedLog(level, category, message, data = null) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = {
                timestamp,
                level,
                category,
                message,
                data
            };
            
            // Log to browser console with appropriate level
            const consoleMethod = console[level] || console.log;
            if (data) {
                consoleMethod(`[${category}] ${message}`, data);
            } else {
                consoleMethod(`[${category}] ${message}`);
            }
            
            // Add to internal log storage
            const displayMessage = `${timestamp} [${level.toUpperCase()}] [${category}] ${message}`;
            logs.push(displayMessage);
            if (data) {
                logs.push(`${timestamp} [${level.toUpperCase()}] [${category}] Data: ${JSON.stringify(data, null, 2)}`);
            }
            
            updateConsoleLogs();
        }
        
        // Override console methods to capture all logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            logs.push(new Date().toLocaleTimeString() + ' [LOG]: ' + args.join(' '));
            updateConsoleLogs();
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            logs.push(new Date().toLocaleTimeString() + ' [ERROR]: ' + args.join(' '));
            updateConsoleLogs();
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            logs.push(new Date().toLocaleTimeString() + ' [WARN]: ' + args.join(' '));
            updateConsoleLogs();
        };
        
        function updateConsoleLogs() {
            document.getElementById('console-logs').innerHTML = 
                '<pre>' + logs.slice(-10).join('\n') + '</pre>';
        }
        
        function clearLogs() {
            logs = [];
            updateConsoleLogs();
        }        
 
       // Enhanced IndexedDB Helper Functions
        async function openIndexedDB() {
            return new Promise((resolve, reject) => {
                if (!('indexedDB' in window)) {
                    const error = new Error('IndexedDB not supported in this browser');
                    enhancedLog(logLevels.ERROR, 'IndexedDB', 'Browser compatibility issue', { 
                        userAgent: navigator.userAgent,
                        error: error.message 
                    });
                    reject(error);
                    return;
                }
                
                const dbName = 'HaulingQROfflineDB';
                enhancedLog(logLevels.DEBUG, 'IndexedDB', `Opening database: ${dbName}`);
                
                const request = indexedDB.open(dbName, 1);
                
                request.onsuccess = (event) => {
                    const db = event.target.result;
                    enhancedLog(logLevels.INFO, 'IndexedDB', 'Database opened successfully', {
                        name: db.name,
                        version: db.version,
                        objectStoreNames: Array.from(db.objectStoreNames)
                    });
                    resolve(db);
                };
                
                request.onerror = (event) => {
                    const error = new Error(`Failed to open IndexedDB: ${event.target.error}`);
                    enhancedLog(logLevels.ERROR, 'IndexedDB', 'Database open failed', {
                        error: event.target.error,
                        errorCode: event.target.errorCode
                    });
                    reject(error);
                };
                
                request.onupgradeneeded = (event) => {
                    enhancedLog(logLevels.INFO, 'IndexedDB', 'Database upgrade needed - database may not exist yet');
                    const error = new Error('Database not initialized yet');
                    reject(error);
                };
                
                request.onblocked = (event) => {
                    const error = new Error('Database open blocked - close other tabs using this database');
                    enhancedLog(logLevels.WARN, 'IndexedDB', 'Database open blocked', {
                        suggestion: 'Close other tabs using this database and try again'
                    });
                    reject(error);
                };
            });
        }
        
        async function getAllFromStore(db, storeName) {
            return new Promise((resolve, reject) => {
                try {
                    if (!db.objectStoreNames.contains(storeName)) {
                        const error = new Error(`Object store '${storeName}' not found`);
                        enhancedLog(logLevels.ERROR, 'IndexedDB', 'Store not found', {
                            storeName,
                            availableStores: Array.from(db.objectStoreNames)
                        });
                        reject(error);
                        return;
                    }
                    
                    enhancedLog(logLevels.DEBUG, 'IndexedDB', `Reading all data from store: ${storeName}`);
                    
                    const transaction = db.transaction([storeName], 'readonly');
                    const store = transaction.objectStore(storeName);
                    const getAllRequest = store.getAll();
                    
                    getAllRequest.onsuccess = () => {
                        const data = getAllRequest.result;
                        enhancedLog(logLevels.INFO, 'IndexedDB', `Retrieved ${data.length} items from ${storeName}`);
                        resolve(data);
                    };
                    
                    getAllRequest.onerror = () => {
                        const error = new Error(`Failed to read from store '${storeName}'`);
                        enhancedLog(logLevels.ERROR, 'IndexedDB', 'Store read failed', {
                            storeName,
                            error: getAllRequest.error
                        });
                        reject(error);
                    };
                    
                    transaction.onerror = () => {
                        const error = new Error(`Transaction failed for store '${storeName}'`);
                        enhancedLog(logLevels.ERROR, 'IndexedDB', 'Transaction failed', {
                            storeName,
                            error: transaction.error
                        });
                        reject(error);
                    };
                    
                    transaction.onabort = () => {
                        const error = new Error(`Transaction aborted for store '${storeName}'`);
                        enhancedLog(logLevels.WARN, 'IndexedDB', 'Transaction aborted', {
                            storeName,
                            error: transaction.error
                        });
                        reject(error);
                    };
                    
                } catch (error) {
                    enhancedLog(logLevels.ERROR, 'IndexedDB', 'Exception in getAllFromStore', {
                        storeName,
                        error: error.message,
                        stack: error.stack
                    });
                    reject(error);
                }
            });
        }
        
        async function clearStore(db, storeName) {
            return new Promise((resolve, reject) => {
                try {
                    if (!db.objectStoreNames.contains(storeName)) {
                        const error = new Error(`Object store '${storeName}' not found`);
                        enhancedLog(logLevels.ERROR, 'IndexedDB', 'Store not found for clearing', {
                            storeName,
                            availableStores: Array.from(db.objectStoreNames)
                        });
                        reject(error);
                        return;
                    }
                    
                    enhancedLog(logLevels.DEBUG, 'IndexedDB', `Clearing all data from store: ${storeName}`);
                    
                    const transaction = db.transaction([storeName], 'readwrite');
                    const store = transaction.objectStore(storeName);
                    const clearRequest = store.clear();
                    
                    clearRequest.onsuccess = () => {
                        enhancedLog(logLevels.INFO, 'IndexedDB', `Successfully cleared store: ${storeName}`);
                        resolve();
                    };
                    
                    clearRequest.onerror = () => {
                        const error = new Error(`Failed to clear store '${storeName}'`);
                        enhancedLog(logLevels.ERROR, 'IndexedDB', 'Store clear failed', {
                            storeName,
                            error: clearRequest.error
                        });
                        reject(error);
                    };
                    
                    transaction.onerror = () => {
                        const error = new Error(`Transaction failed while clearing store '${storeName}'`);
                        enhancedLog(logLevels.ERROR, 'IndexedDB', 'Clear transaction failed', {
                            storeName,
                            error: transaction.error
                        });
                        reject(error);
                    };
                    
                    transaction.onabort = () => {
                        const error = new Error(`Transaction aborted while clearing store '${storeName}'`);
                        enhancedLog(logLevels.WARN, 'IndexedDB', 'Clear transaction aborted', {
                            storeName,
                            error: transaction.error
                        });
                        reject(error);
                    };
                    
                } catch (error) {
                    enhancedLog(logLevels.ERROR, 'IndexedDB', 'Exception in clearStore', {
                        storeName,
                        error: error.message,
                        stack: error.stack
                    });
                    reject(error);
                }
            });
        }
        
        // Helper functions for safe data access
        function safeGetValue(value, defaultValue = 'N/A') {
            try {
                return value !== null && value !== undefined && value !== '' ? value : defaultValue;
            } catch (error) {
                enhancedLog(logLevels.WARN, 'QR-Inspection', 'Safe value access failed', { error: error.message });
                return defaultValue;
            }
        }
        
        function safeFormatTimestamp(timestamp) {
            try {
                if (!timestamp) return 'N/A';
                const date = new Date(timestamp);
                if (isNaN(date.getTime())) return 'Invalid Date';
                return date.toLocaleString();
            } catch (error) {
                enhancedLog(logLevels.WARN, 'QR-Inspection', 'Timestamp formatting failed', { 
                    timestamp, 
                    error: error.message 
                });
                return 'Format Error';
            }
        }        

        // Environment Detection
        function checkEnvironment() {
            const info = {
                userAgent: navigator.userAgent,
                online: navigator.onLine,
                url: window.location.href,
                pathname: window.location.pathname,
                standalone: window.navigator.standalone,
                displayMode: window.matchMedia('(display-mode: standalone)').matches,
                serviceWorkerSupported: 'serviceWorker' in navigator,
                cacheSupported: 'caches' in window
            };
            
            document.getElementById('env-info').innerHTML = 
                '<pre>' + JSON.stringify(info, null, 2) + '</pre>';
            
            console.log('Environment Info:', info);
        }
        
        // Service Worker Check with enhanced error handling
        async function checkServiceWorker() {
            const info = document.getElementById('sw-info');
            
            try {
                if (!info) {
                    throw new Error('sw-info element not found');
                }
                
                info.innerHTML = '<div class="info">🔍 Checking Service Worker...</div>';
                
                if ('serviceWorker' in navigator) {
                    try {
                        const registration = await navigator.serviceWorker.getRegistration();
                        if (registration) {
                            const swInfo = {
                                scope: registration.scopee,
                            state: registration.active?.state,
                            scriptURL: registration.active?.scriptURL,
                            updateViaCache: registration.updateViaCache
                        };
                        
                        info.innerHTML = '<div class="success">✅ Service Worker Registered</div>' +
                                       '<pre>' + JSON.stringify(swInfo, null, 2) + '</pre>';
                        
                        console.log('Service Worker Info:', swInfo);
                        
                        // Send PWA mode info to service worker
                        const isPWAMode = window.matchMedia('(display-mode: standalone)').matches || 
                                         window.navigator.standalone === true;
                        
                        if (registration.active) {
                            registration.active.postMessage({
                                type: 'PWA_MODE_INFO',
                                isPWAMode: isPWAMode,
                                currentPath: window.location.pathname
                            });
                            console.log('Sent PWA mode info to SW:', isPWAMode);
                        }
                    } else {
                        info.innerHTML = '<div class="error">❌ No Service Worker Registration</div>';
                        console.log('No service worker registration found');
                    }
                } catch (error) {
                    info.innerHTML = '<div class="error">❌ Error: ' + error.message + '</div>';
                    console.error('Service Worker Error:', error);
                }
            } else {
                info.innerHTML = '<div class="error">❌ Service Worker Not Supported</div>';
            }
        }
        
        // Cache Check with enhanced error handling
        async function checkCaches() {
            const info = document.getElementById('cache-info');
            
            try {
                if (!info) {
                    throw new Error('cache-info element not found');
                }
                
                info.innerHTML = '<div class="info">🔍 Checking Caches...</div>';
                
                if ('caches' in window) {
                try {
                    const cacheNames = await caches.keys();
                    let cacheInfo = '<div class="success">✅ Found ' + cacheNames.length + ' caches</div>';
                    
                    for (const cacheName of cacheNames) {
                        const cache = await caches.open(cacheName);
                        const keys = await cache.keys();
                        cacheInfo += '<h4>' + cacheName + ' (' + keys.length + ' items)</h4>';
                        cacheInfo += '<ul>';
                        keys.forEach(request => {
                            cacheInfo += '<li>' + request.url + '</li>';
                        });
                        cacheInfo += '</ul>';
                    }
                    
                    info.innerHTML = cacheInfo;
                    console.log('Cache Names:', cacheNames);
                } catch (error) {
                    info.innerHTML = '<div class="error">❌ Cache Error: ' + error.message + '</div>';
                    console.error('Cache Error:', error);
                }
            } else {
                info.innerHTML = '<div class="error">❌ Cache API Not Supported</div>';
            }
        }
        
        // PWA Mode Test with enhanced error handling
        function testPWAMode() {
            try {
                const info = document.getElementById('pwa-info');
                if (!info) {
                    throw new Error('pwa-info element not found');
                }
                
                info.innerHTML = '<div class="info">🔍 Testing PWA Mode...</div>';
                
                const isPWAStandalone = window.matchMedia('(display-mode: standalone)').matches;
                const isiOSStandalone = window.navigator.standalone === true;
                const isPWAMode = isPWAStandalone || isiOSStandalone;
            
            const info = {
                displayModeStandalone: isPWAStandalone,
                iOSStandalone: isiOSStandalone,
                detectedPWAMode: isPWAMode,
                currentPath: window.location.pathname,
                isPWAPage: window.location.pathname === '/driver-connect' || window.location.pathname === '/trip-scanner'
            };
            
            let html = isPWAMode ? 
                '<div class="success">✅ PWA Mode Detected</div>' : 
                '<div class="info">ℹ️ Browser Mode Detected</div>';
            
            html += '<pre>' + JSON.stringify(info, null, 2) + '</pre>';
            
            document.getElementById('pwa-info').innerHTML = html;
            console.log('PWA Mode Test:', info);
        }
        
        // Navigation Test
        async function testNavigation(path) {
            const info = document.getElementById('nav-info');
            info.innerHTML = '<div class="info">Testing navigation to ' + path + '...</div>';
            
            try {
                // Test if the path is cached
                const cachedResponse = await caches.match(path);
                const indexCached = await caches.match('/');
                const indexHtmlCached = await caches.match('/index.html');
                
                const result = {
                    path: path,
                    directlyCached: !!cachedResponse,
                    indexCached: !!indexCached,
                    indexHtmlCached: !!indexHtmlCached,
                    canNavigateOffline: !!(cachedResponse || indexCached || indexHtmlCached)
                };
                
                let html = result.canNavigateOffline ? 
                    '<div class="success">✅ Can navigate offline to ' + path + '</div>' :
                    '<div class="error">❌ Cannot navigate offline to ' + path + '</div>';
                
                html += '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
                info.innerHTML = html;
                
                console.log('Navigation Test Result:', result);
            } catch (error) {
                info.innerHTML = '<div class="error">❌ Navigation Test Error: ' + error.message + '</div>';
                console.error('Navigation Test Error:', error);
            }
        }       
 
        // Enhanced IndexedDB Check with comprehensive error handling
        async function checkIndexedDB() {
            const info = document.getElementById('indexeddb-info');
            info.innerHTML = '<div class="info">🔍 Checking IndexedDB availability and status...</div>';
            
            try {
                enhancedLog(logLevels.INFO, 'IndexedDB', 'Starting IndexedDB check');
                
                const db = await openIndexedDB();
                const storeNames = Array.from(db.objectStoreNames);
                
                let html = '<div class="success">✅ IndexedDB Available and Accessible</div>';
                html += `<h4>Database: ${db.name} (Version: ${db.version})</h4>`;
                html += '<h4>Object Stores:</h4><ul>';
                
                // Enhanced store information gathering
                const storeInfo = [];
                for (const storeName of storeNames) {
                    try {
                        enhancedLog(logLevels.DEBUG, 'IndexedDB', `Checking store: ${storeName}`);
                        
                        const transaction = db.transaction([storeName], 'readonly');
                        const store = transaction.objectStore(storeName);
                        
                        // Get count and key range info
                        const countRequest = store.count();
                        const keyRangeRequest = store.getAllKeys();
                        
                        await new Promise((resolve, reject) => {
                            let count = 0;
                            let keys = [];
                            let completed = 0;
                            
                            countRequest.onsuccess = () => {
                                count = countRequest.result;
                                completed++;
                                if (completed === 2) resolve({ count, keys });
                            };
                            
                            keyRangeRequest.onsuccess = () => {
                                keys = keyRangeRequest.result;
                                completed++;
                                if (completed === 2) resolve({ count, keys });
                            };
                            
                            countRequest.onerror = keyRangeRequest.onerror = () => {
                                enhancedLog(logLevels.WARN, 'IndexedDB', `Failed to get info for store: ${storeName}`);
                                resolve({ count: 'Unknown', keys: [] });
                            };
                            
                            // Timeout after 5 seconds
                            setTimeout(() => {
                                if (completed < 2) {
                                    enhancedLog(logLevels.WARN, 'IndexedDB', `Timeout getting info for store: ${storeName}`);
                                    resolve({ count: 'Timeout', keys: [] });
                                }
                            }, 5000);
                        }).then(({ count, keys }) => {
                            storeInfo.push({ storeName, count, keys: keys.slice(0, 5) }); // Show first 5 keys
                            
                            const listItem = document.createElement('li');
                            listItem.innerHTML = `<strong>${storeName}:</strong> ${count} items` + 
                                               (keys.length > 0 ? ` (Keys: ${keys.slice(0, 3).join(', ')}${keys.length > 3 ? '...' : ''})` : '');
                            document.querySelector('#indexeddb-info ul').appendChild(listItem);
                        });
                        
                    } catch (error) {
                        enhancedLog(logLevels.ERROR, 'IndexedDB', `Error checking store: ${storeName}`, {
                            error: error.message
                        });
                        
                        const listItem = document.createElement('li');
                        listItem.innerHTML = `<strong>${storeName}:</strong> <span class="error">Error: ${error.message}</span>`;
                        document.querySelector('#indexeddb-info ul').appendChild(listItem);
                    }
                }
                
                html += '</ul>';
                
                // Add database health summary
                html += '<div style="background: #e7f3ff; padding: 10px; border-radius: 5px; margin: 10px 0;">';
                html += '<strong>🏥 Database Health Summary:</strong><br>';
                html += `• Database Name: ${db.name}<br>`;
                html += `• Database Version: ${db.version}<br>`;
                html += `• Object Stores: ${storeNames.length}<br>`;
                html += `• Browser: ${navigator.userAgent.split(' ').pop()}<br>`;
                html += `• IndexedDB Support Level: Full`;
                html += '</div>';
                
                info.innerHTML = html;
                
                enhancedLog(logLevels.INFO, 'IndexedDB', 'Database check completed successfully', {
                    storeCount: storeNames.length,
                    storeNames,
                    storeInfo
                });
                
                db.close();
                
            } catch (error) {
                enhancedLog(logLevels.ERROR, 'IndexedDB', 'Database check failed', {
                    error: error.message,
                    stack: error.stack
                });
                
                let html = '<div class="error">❌ IndexedDB Check Failed</div>';
                html += `<div class="error">Error: ${error.message}</div>`;
                
                // Provide user-friendly troubleshooting suggestions
                html += '<div style="background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #ffc107;">';
                html += '<strong>🔧 Troubleshooting Suggestions:</strong><br>';
                
                if (error.message.includes('not supported')) {
                    html += '• Your browser does not support IndexedDB<br>';
                    html += '• Try using a modern browser (Chrome, Firefox, Safari, Edge)<br>';
                    html += '• Check if you are in private/incognito mode (IndexedDB may be disabled)<br>';
                } else if (error.message.includes('not initialized')) {
                    html += '• The offline database has not been created yet<br>';
                    html += '• Try scanning some QR codes offline to initialize the database<br>';
                    html += '• Use the "Simulate QR Scan" buttons to create test data<br>';
                } else if (error.message.includes('blocked')) {
                    html += '• Close other tabs using this application<br>';
                    html += '• Refresh the page and try again<br>';
                    html += '• Clear browser data if the problem persists<br>';
                } else {
                    html += '• Refresh the page and try again<br>';
                    html += '• Check browser console for detailed error information<br>';
                    html += '• Clear browser data if the problem persists<br>';
                    html += '• Try using a different browser<br>';
                }
                
                html += '</div>';
                
                info.innerHTML = html;
            }
        }      
  
        // Enhanced QR Data Inspection with comprehensive error handling
        async function inspectQRData() {
            const info = document.getElementById('qr-inspection-info');
            info.innerHTML = '<div class="info">🔍 Inspecting stored QR data...</div>';
            
            try {
                enhancedLog(logLevels.INFO, 'QR-Inspection', 'Starting QR data inspection');
                
                const db = await openIndexedDB();
                const connections = await getAllFromStore(db, 'connectionQueue');
                
                if (connections.length === 0) {
                    info.innerHTML = '<div class="info">ℹ️ No QR data stored offline</div>' +
                                   '<div style="background: #e7f3ff; padding: 10px; border-radius: 5px; margin: 10px 0;">' +
                                   '<strong>💡 How to create test data:</strong><br>' +
                                   '• Use "Simulate Driver QR Scan" button<br>' +
                                   '• Use "Simulate Truck QR Scan" button<br>' +
                                   '• Scan actual QR codes while offline in the PWA' +
                                   '</div>';
                    
                    enhancedLog(logLevels.INFO, 'QR-Inspection', 'No QR connections found in storage');
                    db.close();
                    return;
                }
                
                enhancedLog(logLevels.INFO, 'QR-Inspection', `Found ${connections.length} stored connections`);
                
                // Display stored connections with enhanced error handling for data parsing
                let html = `<div class="success">✅ Found ${connections.length} stored QR connection${connections.length > 1 ? 's' : ''}</div>`;
                html += '<h3>Stored QR Connections:</h3>';
                
                connections.forEach((connection, index) => {
                    try {
                        html += '<div style="border: 2px solid #007bff; margin: 10px 0; padding: 15px; border-radius: 5px; background-color: #f8f9fa;">';
                        html += `<h4 style="margin-top: 0; color: #007bff;">Connection ${index + 1}</h4>`;
                        
                        // Safely extract data with error handling
                        let driverData, truckData;
                        try {
                            driverData = connection.apiPayload?.driver_qr_data;
                            truckData = connection.apiPayload?.truck_qr_data;
                        } catch (parseError) {
                            enhancedLog(logLevels.WARN, 'QR-Inspection', `Failed to parse connection ${index + 1} data`, {
                                connectionId: connection.id,
                                error: parseError.message
                            });
                        }
                        
                        // Display key information with safe access
                        html += '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">';
                        html += '<div>';
                        html += `<strong>Driver ID:</strong> ${safeGetValue(connection.employeeId || driverData?.employee_id, 'N/A')}<br>`;
                        html += `<strong>Truck ID:</strong> ${safeGetValue(connection.truckId || truckData?.id, 'N/A')}<br>`;
                        html += `<strong>Action:</strong> ${safeGetValue(connection.action, 'N/A')}<br>`;
                        html += '</div>';
                        html += '<div>';
                        html += `<strong>Status:</strong> ${safeGetValue(connection.status, 'N/A')}<br>`;
                        html += `<strong>Timestamp:</strong> ${safeFormatTimestamp(connection.timestamp)}<br>`;
                        html += `<strong>Priority:</strong> ${safeGetValue(connection.priority, 'N/A')}<br>`;
                        html += '</div>';
                        html += '</div>';
                        
                        // Show detailed driver QR data if available
                        if (driverData) {
                            html += '<details style="margin-top: 10px;">';
                            html += '<summary style="cursor: pointer; font-weight: bold;">Driver QR Details</summary>';
                            html += '<pre style="background: #e9ecef; padding: 10px; margin: 5px 0; border-radius: 3px; font-size: 12px;">';
                            try {
                                html += JSON.stringify(driverData, null, 2);
                            } catch (jsonError) {
                                html += `Error displaying driver data: ${jsonError.message}`;
                                enhancedLog(logLevels.WARN, 'QR-Inspection', 'Failed to stringify driver data', {
                                    connectionId: connection.id,
                                    error: jsonError.message
                                });
                            }
                            html += '</pre>';
                            html += '</details>';
                        }
                        
                        // Show detailed truck QR data if available
                        if (truckData) {
                            html += '<details style="margin-top: 5px;">';
                            html += '<summary style="cursor: pointer; font-weight: bold;">Truck QR Details</summary>';
                            html += '<pre style="background: #e9ecef; padding: 10px; margin: 5px 0; border-radius: 3px; font-size: 12px;">';
                            try {
                                html += JSON.stringify(truckData, null, 2);
                            } catch (jsonError) {
                                html += `Error displaying truck data: ${jsonError.message}`;
                                enhancedLog(logLevels.WARN, 'QR-Inspection', 'Failed to stringify truck data', {
                                    connectionId: connection.id,
                                    error: jsonError.message
                                });
                            }
                            html += '</pre>';
                            html += '</details>';
                        }
                        
                        // Show sync metadata if available
                        if (connection.syncMetadata) {
                            html += '<details style="margin-top: 5px;">';
                            html += '<summary style="cursor: pointer; font-weight: bold;">Sync Metadata</summary>';
                            html += '<pre style="background: #e9ecef; padding: 10px; margin: 5px 0; border-radius: 3px; font-size: 12px;">';
                            try {
                                html += JSON.stringify(connection.syncMetadata, null, 2);
                            } catch (jsonError) {
                                html += `Error displaying sync metadata: ${jsonError.message}`;
                                enhancedLog(logLevels.WARN, 'QR-Inspection', 'Failed to stringify sync metadata', {
                                    connectionId: connection.id,
                                    error: jsonError.message
                                });
                            }
                            html += '</pre>';
                            html += '</details>';
                        }
                        
                        html += '</div>';
                        
                    } catch (connectionError) {
                        enhancedLog(logLevels.ERROR, 'QR-Inspection', `Error processing connection ${index + 1}`, {
                            connectionId: connection.id,
                            error: connectionError.message,
                            stack: connectionError.stack
                        });
                        
                        html += '<div style="border: 2px solid #dc3545; margin: 10px 0; padding: 15px; border-radius: 5px; background-color: #f8d7da;">';
                        html += `<h4 style="margin-top: 0; color: #dc3545;">Connection ${index + 1} - Error</h4>`;
                        html += `<div class="error">Failed to display connection data: ${connectionError.message}</div>`;
                        html += '<div style="font-size: 12px; margin-top: 5px;">Check console logs for detailed error information</div>';
                        html += '</div>';
                    }
                });
                
                // Add data integrity summary
                html += '<div style="background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #28a745;">';
                html += '<strong>📊 Data Integrity Summary:</strong><br>';
                html += `• Total connections: ${connections.length}<br>`;
                html += `• Database: HaulingQROfflineDB<br>`;
                html += `• Store: connectionQueue<br>`;
                html += `• Inspection completed: ${new Date().toLocaleString()}`;
                html += '</div>';
                
                info.innerHTML = html;
                
                enhancedLog(logLevels.INFO, 'QR-Inspection', 'QR data inspection completed successfully', {
                    count: connections.length,
                    connectionIds: connections.map(c => c.id).filter(Boolean)
                });
                
                db.close();
                
            } catch (error) {
                enhancedLog(logLevels.ERROR, 'QR-Inspection', 'QR data inspection failed', {
                    error: error.message,
                    stack: error.stack
                });
                
                let html = '<div class="error">❌ QR Data Inspection Failed</div>';
                html += `<div class="error">Error: ${error.message}</div>`;
                
                // Provide specific troubleshooting based on error type
                html += '<div style="background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #ffc107;">';
                html += '<strong>🔧 Troubleshooting Steps:</strong><br>';
                
                if (error.message.includes('not supported')) {
                    html += '• IndexedDB is not supported in your browser<br>';
                    html += '• Try using a modern browser (Chrome, Firefox, Safari, Edge)<br>';
                    html += '• Disable private/incognito mode if active<br>';
                } else if (error.message.includes('not initialized') || error.message.includes('not found')) {
                    html += '• The offline database has not been created yet<br>';
                    html += '• Try creating test data using the simulation buttons<br>';
                    html += '• Scan QR codes while offline to initialize the database<br>';
                } else if (error.message.includes('blocked')) {
                    html += '• Close other tabs using this application<br>';
                    html += '• Refresh the page and try again<br>';
                } else {
                    html += '• Check the browser console for detailed error logs<br>';
                    html += '• Try refreshing the page<br>';
                    html += '• Clear browser data if the problem persists<br>';
                }
                
                html += '• Contact support if the issue continues<br>';
                html += '</div>';
                
                info.innerHTML = html;
            }
        }  
      
        // Enhanced QR Data Clearing with comprehensive error handling
        async function clearQRData() {
            const info = document.getElementById('qr-inspection-info');
            
            try {
                enhancedLog(logLevels.INFO, 'QR-Clear', 'Starting QR data clear operation');
                
                // Show enhanced confirmation dialog with more information
                const confirmed = confirm(
                    'Are you sure you want to clear all stored QR data?\n\n' +
                    '⚠️ This action will:\n' +
                    '• Remove all offline QR scan data\n' +
                    '• Delete pending sync connections\n' +
                    '• Cannot be undone\n\n' +
                    'Click OK to proceed or Cancel to abort.'
                );
                
                if (!confirmed) {
                    info.innerHTML = '<div class="info">ℹ️ Clear operation cancelled by user</div>';
                    enhancedLog(logLevels.INFO, 'QR-Clear', 'Clear operation cancelled by user');
                    return;
                }
                
                info.innerHTML = '<div class="info">🗑️ Clearing stored QR data...</div>';
                
                const db = await openIndexedDB();
                
                // First, get count of items to be cleared for better user feedback
                const connections = await getAllFromStore(db, 'connectionQueue');
                const itemCount = connections.length;
                
                if (itemCount === 0) {
                    info.innerHTML = '<div class="info">ℹ️ No QR data to clear - storage is already empty</div>' +
                                   '<div style="background: #e7f3ff; padding: 10px; border-radius: 5px; margin: 10px 0;">' +
                                   '<strong>💡 Storage Status:</strong><br>' +
                                   '• Database: HaulingQROfflineDB<br>' +
                                   '• Store: connectionQueue<br>' +
                                   '• Current items: 0<br>' +
                                   '• Status: Already empty' +
                                   '</div>';
                    
                    enhancedLog(logLevels.INFO, 'QR-Clear', 'No QR connections found to clear');
                    db.close();
                    return;
                }
                
                enhancedLog(logLevels.INFO, 'QR-Clear', `Clearing ${itemCount} QR connections`);
                
                // Show progress for large datasets
                if (itemCount > 10) {
                    info.innerHTML = `<div class="info">🗑️ Clearing ${itemCount} QR connections... This may take a moment.</div>`;
                }
                
                // Clear all stored QR connections using the helper function
                await clearStore(db, 'connectionQueue');
                
                // Success feedback with detailed information
                let html = `<div class="success">✅ Successfully cleared ${itemCount} QR connection${itemCount > 1 ? 's' : ''}</div>`;
                html += '<div class="info">ℹ️ All stored QR data has been removed from IndexedDB</div>';
                
                // Add operation summary
                html += '<div style="background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #28a745;">';
                html += '<strong>🧹 Clear Operation Summary:</strong><br>';
                html += `• Items cleared: ${itemCount}<br>`;
                html += `• Database: HaulingQROfflineDB<br>`;
                html += `• Store: connectionQueue<br>`;
                html += `• Completed: ${new Date().toLocaleString()}<br>`;
                html += '• Status: Storage is now empty';
                html += '</div>';
                
                // Add next steps
                html += '<div style="background: #e7f3ff; padding: 10px; border-radius: 5px; margin: 10px 0;">';
                html += '<strong>📋 Next Steps:</strong><br>';
                html += '• Use "Inspect Stored QR Data" to verify empty storage<br>';
                html += '• Use simulation buttons to create new test data<br>';
                html += '• Scan QR codes offline to populate storage again';
                html += '</div>';
                
                info.innerHTML = html;
                
                enhancedLog(logLevels.INFO, 'QR-Clear', 'QR data cleared successfully', {
                    itemsCleared: itemCount,
                    timestamp: new Date().toISOString()
                });
                
                // Auto-refresh the inspection to show cleared state
                setTimeout(() => {
                    info.innerHTML += '<div class="info">🔄 Refreshing inspection to verify cleared state...</div>';
                    setTimeout(inspectQRData, 1000);
                }, 2000);
                
                db.close();
                
            } catch (error) {
                enhancedLog(logLevels.ERROR, 'QR-Clear', 'QR data clear operation failed', {
                    error: error.message,
                    stack: error.stack
                });
                
                let html = '<div class="error">❌ QR Data Clear Failed</div>';
                html += `<div class="error">Error: ${error.message}</div>`;
                
                // Provide specific troubleshooting based on error type
                html += '<div style="background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #ffc107;">';
                html += '<strong>🔧 Clear Operation Troubleshooting:</strong><br>';
                
                if (error.message.includes('not supported')) {
                    html += '• IndexedDB is not supported in your browser<br>';
                    html += '• Try using a modern browser (Chrome, Firefox, Safari, Edge)<br>';
                    html += '• Disable private/incognito mode if active<br>';
                } else if (error.message.includes('not initialized') || error.message.includes('not found')) {
                    html += '• The offline database does not exist yet<br>';
                    html += '• No data to clear - this is normal for new installations<br>';
                    html += '• Try creating test data first, then clearing it<br>';
                } else if (error.message.includes('blocked')) {
                    html += '• Close other tabs using this application<br>';
                    html += '• Refresh the page and try again<br>';
                    html += '• Wait a moment and retry the operation<br>';
                } else if (error.message.includes('Transaction')) {
                    html += '• Database transaction failed during clear operation<br>';
                    html += '• Try refreshing the page and attempting again<br>';
                    html += '• Check if other tabs are using the database<br>';
                } else {
                    html += '• Check the browser console for detailed error logs<br>';
                    html += '• Try refreshing the page and attempting again<br>';
                    html += '• Clear browser data if the problem persists<br>';
                }
                
                html += '• Contact support if the issue continues<br>';
                html += '</div>';
                
                // Add recovery suggestions
                html += '<div style="background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #17a2b8;">';
                html += '<strong>🔄 Recovery Options:</strong><br>';
                html += '• Try using browser developer tools to manually clear IndexedDB<br>';
                html += '• Clear all browser data for this site<br>';
                html += '• Use a different browser as a workaround<br>';
                html += '• Report this issue to the development team';
                html += '</div>';
                
                info.innerHTML = html;
            }
        }    
    
        // Enhanced PWA Status Hook Communication Test with comprehensive error handling
        async function testPWAStatusHook() {
            const info = document.getElementById('pwa-hook-info');
            info.innerHTML = '<div class="info">🔄 Testing PWA Status Hook Communication...</div>';
            
            const testResults = [];
            let communicationErrors = [];
            
            try {
                enhancedLog(logLevels.INFO, 'PWA-Communication', 'Starting PWA status hook communication test');
                
                // Test 1: Enhanced PWA mode detection with multiple methods
                let isPWAMode = false;
                const detectionMethods = [];
                
                try {
                    // Method 1: Display mode media query
                    const standaloneMatch = window.matchMedia('(display-mode: standalone)').matches;
                    detectionMethods.push({ method: 'display-mode media query', result: standaloneMatch });
                    
                    // Method 2: iOS standalone
                    const iOSStandalone = window.navigator.standalone === true;
                    detectionMethods.push({ method: 'iOS navigator.standalone', result: iOSStandalone });
                    
                    // Method 3: Android app referrer
                    const androidApp = document.referrer.includes('android-app://');
                    detectionMethods.push({ method: 'Android app referrer', result: androidApp });
                    
                    // Method 4: URL parameters (some PWA launchers add these)
                    const urlParams = new URLSearchParams(window.location.search);
                    const pwaParam = urlParams.has('pwa') || urlParams.has('standalone');
                    detectionMethods.push({ method: 'URL parameters', result: pwaParam });
                    
                    isPWAMode = standaloneMatch || iOSStandalone || androidApp || pwaParam;
                    
                    testResults.push({
                        test: 'Enhanced PWA Mode Detection',
                        result: isPWAMode ? 'PWA Mode Detected' : 'Browser Mode Detected',
                        status: 'info',
                        details: detectionMethods
                    });
                    
                    enhancedLog(logLevels.INFO, 'PWA-Communication', 'PWA mode detection completed', {
                        isPWAMode,
                        detectionMethods
                    });
                    
                } catch (detectionError) {
                    enhancedLog(logLevels.ERROR, 'PWA-Communication', 'PWA mode detection failed', {
                        error: detectionError.message
                    });
                    
                    testResults.push({
                        test: 'PWA Mode Detection',
                        result: 'Detection Failed',
                        status: 'error',
                        error: detectionError.message
                    });
                    
                    communicationErrors.push(`PWA Detection: ${detectionError.message}`);
                }
                
                // Test 2: Service Worker API availability
                try {
                    if ('serviceWorker' in navigator) {
                        testResults.push({
                            test: 'Service Worker API Support',
                            result: 'Available',
                            status: 'success'
                        });
                        
                        enhancedLog(logLevels.INFO, 'PWA-Communication', 'Service Worker API is available');
                        
                        // Test 3: Service Worker registration status
                        try {
                            const registration = await navigator.serviceWorker.getRegistration();
                            
                            if (registration) {
                                testResults.push({
                                    test: 'Service Worker Registration',
                                    result: `Registered (${registration.scope})`,
                                    status: 'success',
                                    details: {
                                        scope: registration.scope,
                                        scriptURL: registration.active?.scriptURL,
                                        state: registration.active?.state
                                    }
                                });
                                
                                enhancedLog(logLevels.INFO, 'PWA-Communication', 'Service Worker registration found', {
                                    scope: registration.scope,
                                    state: registration.active?.state
                                });
                                
                            } else {
                                testResults.push({
                                    test: 'Service Worker Registration',
                                    result: 'Not Registered',
                                    status: 'error'
                                });
                                
                                communicationErrors.push('Service Worker not registered');
                                enhancedLog(logLevels.ERROR, 'PWA-Communication', 'No service worker registration found');
                            }
                            
                        } catch (registrationError) {
                            testResults.push({
                                test: 'Service Worker Registration',
                                result: 'Check Failed',
                                status: 'error',
                                error: registrationError.message
                            });
                            
                            communicationErrors.push(`Registration Check: ${registrationError.message}`);
                            enhancedLog(logLevels.ERROR, 'PWA-Communication', 'Service worker registration check failed', {
                                error: registrationError.message
                            });
                        }
                        
                        // Test 4: Service Worker controller availability
                        try {
                            if (navigator.serviceWorker.controller) {
                                testResults.push({
                                    test: 'Service Worker Controller',
                                    result: 'Active and Controlling',
                                    status: 'success',
                                    details: {
                                        scriptURL: navigator.serviceWorker.controller.scriptURL,
                                        state: navigator.serviceWorker.controller.state
                                    }
                                });
                                
                                enhancedLog(logLevels.INFO, 'PWA-Communication', 'Service Worker controller is active');
                                
                                // Test 5: Enhanced message sending with error handling
                                try {
                                    const messageTests = [
                                        {
                                            name: 'PWA Mode Status',
                                            message: {
                                                type: 'PWA_MODE_STATUS',
                                                isPWA: isPWAMode,
                                                currentPath: window.location.pathname,
                                                timestamp: new Date().toISOString(),
                                                userAgent: navigator.userAgent,
                                                displayMode: window.matchMedia('(display-mode: standalone)').matches ? 'standalone' : 'browser',
                                                source: 'debug-pwa-test',
                                                detectionMethods
                                            }
                                        },
                                        {
                                            name: 'Location Change',
                                            message: {
                                                type: 'LOCATION_CHANGE',
                                                currentPath: window.location.pathname,
                                                isPWA: isPWAMode,
                                                timestamp: new Date().toISOString(),
                                                source: 'debug-pwa-test'
                                            }
                                        },
                                        {
                                            name: 'Client Ready',
                                            message: {
                                                type: 'CLIENT_READY',
                                                timestamp: new Date().toISOString(),
                                                source: 'debug-pwa-test',
                                                capabilities: {
                                                    indexedDB: 'indexedDB' in window,
                                                    caches: 'caches' in window,
                                                    notifications: 'Notification' in window
                                                }
                                            }
                                        }
                                    ];
                                    
                                    for (const messageTest of messageTests) {
                                        try {
                                            navigator.serviceWorker.controller.postMessage(messageTest.message);
                                            
                                            testResults.push({
                                                test: `${messageTest.name} Message`,
                                                result: 'Sent Successfully',
                                                status: 'success',
                                                details: messageTest.message
                                            });
                                            
                                            enhancedLog(logLevels.INFO, 'PWA-Communication', `${messageTest.name} message sent`, {
                                                messageType: messageTest.message.type
                                            });
                                            
                                        } catch (messageError) {
                                            testResults.push({
                                                test: `${messageTest.name} Message`,
                                                result: 'Send Failed',
                                                status: 'error',
                                                error: messageError.message
                                            });
                                            
                                            communicationErrors.push(`${messageTest.name}: ${messageError.message}`);
                                            enhancedLog(logLevels.ERROR, 'PWA-Communication', `${messageTest.name} message failed`, {
                                                error: messageError.message
                                            });
                                        }
                                    }
                                    
                                } catch (messagingError) {
                                    testResults.push({
                                        test: 'Service Worker Messaging',
                                        result: 'Messaging System Failed',
                                        status: 'error',
                                        error: messagingError.message
                                    });
                                    
                                    communicationErrors.push(`Messaging System: ${messagingError.message}`);
                                    enhancedLog(logLevels.ERROR, 'PWA-Communication', 'Service worker messaging failed', {
                                        error: messagingError.message
                                    });
                                }
                                
                            } else {
                                testResults.push({
                                    test: 'Service Worker Controller',
                                    result: 'No Active Controller',
                                    status: 'error'
                                });
                                
                                communicationErrors.push('No active service worker controller');
                                enhancedLog(logLevels.ERROR, 'PWA-Communication', 'No active service worker controller');
                            }
                            
                        } catch (controllerError) {
                            testResults.push({
                                test: 'Service Worker Controller',
                                result: 'Controller Check Failed',
                                status: 'error',
                                error: controllerError.message
                            });
                            
                            communicationErrors.push(`Controller Check: ${controllerError.message}`);
                            enhancedLog(logLevels.ERROR, 'PWA-Communication', 'Service worker controller check failed', {
                                error: controllerError.message
                            });
                        }
                        
                    } else {
                        testResults.push({
                            test: 'Service Worker API Support',
                            result: 'Not Supported',
                            status: 'error'
                        });
                        
                        communicationErrors.push('Service Worker API not supported');
                        enhancedLog(logLevels.ERROR, 'PWA-Communication', 'Service Worker API not supported');
                    }
                    
                } catch (serviceWorkerError) {
                    testResults.push({
                        test: 'Service Worker System',
                        result: 'System Check Failed',
                        status: 'error',
                        error: serviceWorkerError.message
                    });
                    
                    communicationErrors.push(`Service Worker System: ${serviceWorkerError.message}`);
                    enhancedLog(logLevels.ERROR, 'PWA-Communication', 'Service worker system check failed', {
                        error: serviceWorkerError.message
                    });
                }
                
                // Test 6: Network and environment status
                try {
                    const environmentInfo = {
                        online: navigator.onLine,
                        userAgent: navigator.userAgent,
                        language: navigator.language,
                        platform: navigator.platform,
                        cookieEnabled: navigator.cookieEnabled,
                        doNotTrack: navigator.doNotTrack
                    };
                    
                    testResults.push({
                        test: 'Network & Environment Status',
                        result: navigator.onLine ? 'Online' : 'Offline',
                        status: navigator.onLine ? 'success' : 'warning',
                        details: environmentInfo
                    });
                    
                    enhancedLog(logLevels.INFO, 'PWA-Communication', 'Environment status checked', environmentInfo);
                    
                } catch (environmentError) {
                    testResults.push({
                        test: 'Environment Status',
                        result: 'Check Failed',
                        status: 'error',
                        error: environmentError.message
                    });
                    
                    enhancedLog(logLevels.ERROR, 'PWA-Communication', 'Environment check failed', {
                        error: environmentError.message
                    });
                }            
    
                // Display comprehensive results
                let html = '<h3>🔍 PWA Status Hook Communication Test Results:</h3>';
                
                // Summary section
                const successCount = testResults.filter(r => r.status === 'success').length;
                const errorCount = testResults.filter(r => r.status === 'error').length;
                const warningCount = testResults.filter(r => r.status === 'warning').length;
                
                html += '<div style="background: #e7f3ff; padding: 10px; border-radius: 5px; margin: 10px 0;">';
                html += '<strong>📊 Test Summary:</strong><br>';
                html += `• Total Tests: ${testResults.length}<br>`;
                html += `• Successful: ${successCount}<br>`;
                html += `• Errors: ${errorCount}<br>`;
                html += `• Warnings: ${warningCount}<br>`;
                html += `• Overall Status: ${errorCount === 0 ? '✅ All systems operational' : '⚠️ Issues detected'}`;
                html += '</div>';
                
                // Individual test results
                testResults.forEach(result => {
                    const className = result.status === 'success' ? 'success' : 
                                    result.status === 'error' ? 'error' : 
                                    result.status === 'warning' ? 'info' : 'info';
                    
                    const icon = result.status === 'success' ? '✅' : 
                               result.status === 'error' ? '❌' : 
                               result.status === 'warning' ? '⚠️' : 'ℹ️';
                    
                    html += `<div class="${className}" style="margin: 5px 0; padding: 8px; border-radius: 3px;">`;
                    html += `${icon} <strong>${result.test}:</strong> ${result.result}`;
                    
                    if (result.error) {
                        html += `<br><small style="opacity: 0.8;">Error: ${result.error}</small>`;
                    }
                    
                    if (result.details && typeof result.details === 'object') {
                        html += '<details style="margin-top: 5px;"><summary style="cursor: pointer; font-size: 12px;">Show Details</summary>';
                        html += '<pre style="background: rgba(0,0,0,0.1); padding: 5px; margin: 5px 0; border-radius: 3px; font-size: 11px;">';
                        html += JSON.stringify(result.details, null, 2);
                        html += '</pre></details>';
                    }
                    
                    html += '</div>';
                });
                
                // Error summary if there are issues
                if (communicationErrors.length > 0) {
                    html += '<div style="background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #ffc107;">';
                    html += '<strong>⚠️ Communication Issues Detected:</strong><br>';
                    communicationErrors.forEach(error => {
                        html += `• ${error}<br>`;
                    });
                    html += '</div>';
                    
                    // Troubleshooting suggestions
                    html += '<div style="background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #17a2b8;">';
                    html += '<strong>🔧 Troubleshooting Suggestions:</strong><br>';
                    html += '• Refresh the page to re-register the service worker<br>';
                    html += '• Check if service worker registration is blocked by browser settings<br>';
                    html += '• Ensure you are not in private/incognito mode<br>';
                    html += '• Try clearing browser cache and reloading<br>';
                    html += '• Check browser console for additional error details<br>';
                    html += '• Verify the service worker file (sw.js) is accessible';
                    html += '</div>';
                }
                
                html += '<div class="info" style="margin-top: 15px;">📋 Check browser console for detailed service worker communication logs</div>';
                info.innerHTML = html;
                
                enhancedLog(logLevels.INFO, 'PWA-Communication', 'PWA status hook communication test completed', {
                    totalTests: testResults.length,
                    successCount,
                    errorCount,
                    warningCount,
                    communicationErrors
                });
                
            } catch (error) {
                enhancedLog(logLevels.ERROR, 'PWA-Communication', 'PWA status hook test failed completely', {
                    error: error.message,
                    stack: error.stack
                });
                
                let html = '<div class="error">❌ PWA Status Hook Test Failed</div>';
                html += `<div class="error">Critical Error: ${error.message}</div>`;
                
                html += '<div style="background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #dc3545;">';
                html += '<strong>🚨 Critical Test Failure:</strong><br>';
                html += '• The PWA communication test encountered a critical error<br>';
                html += '• This may indicate a serious browser compatibility issue<br>';
                html += '• Check browser console for detailed error information<br>';
                html += '• Try refreshing the page and running the test again<br>';
                html += '• Consider using a different browser for testing';
                html += '</div>';
                
                info.innerHTML = html;
            }
        }
        
        // Enhanced QR Data Inspection Function with improved UI
        async function inspectQRData() {
            const info = document.getElementById('qr-inspection-info');
            const button = event.target;
            
            // Add loading state to button
            button.classList.add('btn-loading');
            button.disabled = true;
            const originalText = button.innerHTML;
            button.innerHTML = '<span class="status-indicator status-pending"></span>Inspecting...';
            
            // Show loading state with enhanced UI
            info.innerHTML = '<div class="info-box info">🔍 Inspecting stored QR data...</div>';
            
            try {
            
            try {
                enhancedLog(logLevels.INFO, 'QR-Inspection', 'Starting QR data inspection');
                
                const db = await openIndexedDB();
                const connections = await getAllFromStore(db, 'connectionQueue');
                
                if (connections.length === 0) {
                    info.innerHTML = '<div class="info-box warning">' +
                                   '<h4>📭 No QR Data Stored Offline</h4>' +
                                   '<p>The offline storage is currently empty. This could mean:</p>' +
                                   '<ul>' +
                                   '<li>No QR codes have been scanned while offline</li>' +
                                   '<li>All previously stored data has been synced</li>' +
                                   '<li>The database was recently cleared</li>' +
                                   '</ul>' +
                                   '<p><strong>💡 Next Steps:</strong> Use the simulation buttons to create test data or scan QR codes while offline.</p>' +
                                   '</div>';
                    
                    enhancedLog(logLevels.INFO, 'QR-Inspection', 'No QR connections found in storage');
                    db.close();
                    return;
                }
                
                let html = `<div class="info-box success">`;
                html += `<h4>📊 Found ${connections.length} Stored QR Connection${connections.length > 1 ? 's' : ''}</h4>`;
                html += `<p>Displaying all offline QR scan data with detailed information:</p>`;
                html += `</div>`;
                
                connections.forEach((connection, index) => {
                    try {
                        const driverData = connection.apiPayload?.driver_qr_data;
                        const truckData = connection.apiPayload?.truck_qr_data;
                        
                        html += '<div class="info-box success" style="border: 2px solid #28a745; margin: 15px 0; padding: 20px; border-radius: 8px; background-color: #d4edda;">';
                        html += `<h4 style="margin-top: 0; color: #28a745; display: flex; align-items: center;">`;
                        html += `<span class="status-indicator status-online"></span>`;
                        html += `Connection ${index + 1}</h4>`;
                        
                        // Enhanced data display with better formatting and status indicators
                        html += '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0;">';
                        html += '<div style="background: rgba(255,255,255,0.7); padding: 12px; border-radius: 6px;">';
                        html += `<div style="margin-bottom: 8px;"><strong>🚛 Driver ID:</strong> <code>${safeGetValue(connection.employeeId || driverData?.employee_id, 'N/A')}</code></div>`;
                        html += `<div style="margin-bottom: 8px;"><strong>🚚 Truck ID:</strong> <code>${safeGetValue(connection.truckId || truckData?.id, 'N/A')}</code></div>`;
                        html += `<div><strong>⚡ Action:</strong> <span class="badge badge-${connection.action === 'check_in' ? 'success' : 'warning'}">${safeGetValue(connection.action, 'N/A')}</span></div>`;
                        html += '</div>';
                        html += '<div style="background: rgba(255,255,255,0.7); padding: 12px; border-radius: 6px;">';
                        const statusColor = connection.status === 'pending' ? 'warning' : connection.status === 'completed' ? 'success' : 'secondary';
                        html += `<div style="margin-bottom: 8px;"><strong>📊 Status:</strong> <span class="badge badge-${statusColor}">${safeGetValue(connection.status, 'N/A')}</span></div>`;
                        html += `<div style="margin-bottom: 8px;"><strong>🕒 Timestamp:</strong> <small>${safeFormatTimestamp(connection.timestamp)}</small></div>`;
                        html += `<div><strong>🔢 Priority:</strong> <code>${safeGetValue(connection.priority, 'N/A')}</code></div>`;
                        html += '</div>';
                        html += '</div>';
                        
                        // Show detailed driver QR data if available
                        if (driverData) {
                            html += '<details style="margin-top: 10px;">';
                            html += '<summary style="cursor: pointer; font-weight: bold; color: #007bff;">🚛 Driver QR Details</summary>';
                            html += '<pre style="background: #e9ecef; padding: 10px; margin: 5px 0; border-radius: 3px; font-size: 12px;">';
                            try {
                                html += JSON.stringify(driverData, null, 2);
                            } catch (jsonError) {
                                html += `Error displaying driver data: ${jsonError.message}`;
                                enhancedLog(logLevels.WARN, 'QR-Inspection', 'Failed to stringify driver data', {
                                    connectionId: connection.id,
                                    error: jsonError.message
                                });
                            }
                            html += '</pre>';
                            html += '</details>';
                        }
                        
                        // Show detailed truck QR data if available
                        if (truckData) {
                            html += '<details style="margin-top: 5px;">';
                            html += '<summary style="cursor: pointer; font-weight: bold; color: #007bff;">🚚 Truck QR Details</summary>';
                            html += '<pre style="background: #e9ecef; padding: 10px; margin: 5px 0; border-radius: 3px; font-size: 12px;">';
                            try {
                                html += JSON.stringify(truckData, null, 2);
                            } catch (jsonError) {
                                html += `Error displaying truck data: ${jsonError.message}`;
                                enhancedLog(logLevels.WARN, 'QR-Inspection', 'Failed to stringify truck data', {
                                    connectionId: connection.id,
                                    error: jsonError.message
                                });
                            }
                            html += '</pre>';
                            html += '</details>';
                        }
                        
                        // Show sync metadata if available
                        if (connection.syncMetadata) {
                            html += '<details style="margin-top: 5px;">';
                            html += '<summary style="cursor: pointer; font-weight: bold; color: #007bff;">🔄 Sync Metadata</summary>';
                            html += '<pre style="background: #e9ecef; padding: 10px; margin: 5px 0; border-radius: 3px; font-size: 12px;">';
                            try {
                                html += JSON.stringify(connection.syncMetadata, null, 2);
                            } catch (jsonError) {
                                html += `Error displaying sync metadata: ${jsonError.message}`;
                                enhancedLog(logLevels.WARN, 'QR-Inspection', 'Failed to stringify sync metadata', {
                                    connectionId: connection.id,
                                    error: jsonError.message
                                });
                            }
                            html += '</pre>';
                            html += '</details>';
                        }
                        
                        html += '</div>';
                        
                    } catch (connectionError) {
                        enhancedLog(logLevels.ERROR, 'QR-Inspection', `Error processing connection ${index + 1}`, {
                            connectionId: connection.id,
                            error: connectionError.message,
                            stack: connectionError.stack
                        });
                        
                        html += '<div class="info-box error">';
                        html += `<h4>Connection ${index + 1} - Error</h4>`;
                        html += `<div class="error">Failed to display connection data: ${connectionError.message}</div>`;
                        html += '<div style="font-size: 12px; margin-top: 5px;">Check console logs for detailed error information</div>';
                        html += '</div>';
                    }
                });
                
                // Add data integrity summary
                html += '<div class="info-box info">';
                html += '<strong>📊 Data Integrity Summary:</strong><br>';
                html += `• Total connections: ${connections.length}<br>`;
                html += `• Database: HaulingQROfflineDB<br>`;
                html += `• Store: connectionQueue<br>`;
                html += `• Inspection completed: ${new Date().toLocaleString()}`;
                html += '</div>';
                
                info.innerHTML = html;
                
                enhancedLog(logLevels.INFO, 'QR-Inspection', 'QR data inspection completed successfully', {
                    count: connections.length,
                    connectionIds: connections.map(c => c.id).filter(Boolean)
                });
                
                db.close();
                
            } catch (error) {
                enhancedLog(logLevels.ERROR, 'QR-Inspection', 'QR data inspection failed', {
                    error: error.message,
                    stack: error.stack
                });
                
                let html = '<div class="info-box error">';
                html += '<h4>❌ QR Data Inspection Failed</h4>';
                html += `<p><strong>Error:</strong> ${error.message}</p>`;
                
                // Provide specific troubleshooting based on error type
                html += '<div style="margin-top: 15px;">';
                html += '<strong>🔧 Troubleshooting Steps:</strong><br>';
                
                if (error.message.includes('not supported')) {
                    html += '• IndexedDB is not supported in your browser<br>';
                    html += '• Try using a modern browser (Chrome, Firefox, Safari, Edge)<br>';
                    html += '• Disable private/incognito mode if active<br>';
                } else if (error.message.includes('not initialized') || error.message.includes('not found')) {
                    html += '• The offline database has not been created yet<br>';
                    html += '• Try creating test data using the simulation buttons<br>';
                    html += '• Scan QR codes while offline to initialize the database<br>';
                } else if (error.message.includes('blocked')) {
                    html += '• Close other tabs using this application<br>';
                    html += '• Refresh the page and try again<br>';
                } else {
                    html += '• Check the browser console for detailed error logs<br>';
                    html += '• Try refreshing the page<br>';
                    html += '• Clear browser data if the problem persists<br>';
                }
                
                html += '• Contact support if the issue continues<br>';
                html += '</div>';
                html += '</div>';
                
                info.innerHTML = html;
            } finally {
                // Reset button state
                button.classList.remove('btn-loading');
                button.disabled = false;
                button.innerHTML = originalText;
            }
        }
        
        // Enhanced QR Data Clearing Function with improved UI
        async function clearQRData() {
            const info = document.getElementById('qr-inspection-info');
            const button = event.target;
            
            try {
                enhancedLog(logLevels.INFO, 'QR-Clear', 'Starting QR data clear operation');
                
                // Show enhanced confirmation dialog with more information
                const confirmed = confirm(
                    'Are you sure you want to clear all stored QR data?\n\n' +
                    '⚠️ This action will:\n' +
                    '• Remove all offline QR scan data\n' +
                    '• Delete pending sync connections\n' +
                    '• Cannot be undone\n\n' +
                    'Click OK to proceed or Cancel to abort.'
                );
                
                if (!confirmed) {
                    info.innerHTML = '<div class="info-box info">ℹ️ Clear operation cancelled by user</div>';
                    enhancedLog(logLevels.INFO, 'QR-Clear', 'Clear operation cancelled by user');
                    return;
                }
                
                // Add loading state to button
                button.classList.add('btn-loading');
                button.disabled = true;
                const originalText = button.innerHTML;
                button.innerHTML = '<span class="status-indicator status-pending"></span>Clearing...';
                
                info.innerHTML = '<div class="info-box warning">🗑️ Clearing stored QR data...</div>';
                
                const db = await openIndexedDB();
                
                // First, get count of items to be cleared for better user feedback
                const connections = await getAllFromStore(db, 'connectionQueue');
                const itemCount = connections.length;
                
                if (itemCount === 0) {
                    info.innerHTML = '<div class="info-box info">' +
                                   '<h4>ℹ️ No QR Data to Clear</h4>' +
                                   '<p>Storage is already empty</p>' +
                                   '<div style="margin-top: 10px;">' +
                                   '<strong>💡 Storage Status:</strong><br>' +
                                   '• Database: HaulingQROfflineDB<br>' +
                                   '• Store: connectionQueue<br>' +
                                   '• Current items: 0<br>' +
                                   '• Status: Already empty' +
                                   '</div>' +
                                   '</div>';
                    
                    enhancedLog(logLevels.INFO, 'QR-Clear', 'No QR connections found to clear');
                    db.close();
                    return;
                }
                
                enhancedLog(logLevels.INFO, 'QR-Clear', `Clearing ${itemCount} QR connections`);
                
                // Show progress for large datasets
                if (itemCount > 10) {
                    info.innerHTML = `<div class="info-box warning">🗑️ Clearing ${itemCount} QR connections... This may take a moment.</div>`;
                }
                
                // Clear all stored QR connections using the helper function
                await clearStore(db, 'connectionQueue');
                
                // Success feedback with detailed information
                let html = '<div class="info-box success">';
                html += `<h4>✅ Successfully Cleared ${itemCount} QR Connection${itemCount > 1 ? 's' : ''}</h4>`;
                html += '<p>All stored QR data has been removed from IndexedDB</p>';
                
                // Add operation summary
                html += '<div style="margin-top: 15px;">';
                html += '<strong>🧹 Clear Operation Summary:</strong><br>';
                html += `• Items cleared: ${itemCount}<br>`;
                html += `• Database: HaulingQROfflineDB<br>`;
                html += `• Store: connectionQueue<br>`;
                html += `• Completed: ${new Date().toLocaleString()}<br>`;
                html += '• Status: Storage is now empty';
                html += '</div>';
                
                // Add next steps
                html += '<div style="margin-top: 15px;">';
                html += '<strong>📋 Next Steps:</strong><br>';
                html += '• Use "Inspect Stored QR Data" to verify empty storage<br>';
                html += '• Use simulation buttons to create new test data<br>';
                html += '• Scan QR codes offline to populate storage again';
                html += '</div>';
                html += '</div>';
                
                info.innerHTML = html;
                
                enhancedLog(logLevels.INFO, 'QR-Clear', 'QR data cleared successfully', {
                    itemsCleared: itemCount,
                    timestamp: new Date().toISOString()
                });
                
                // Auto-refresh the inspection to show cleared state
                setTimeout(() => {
                    info.innerHTML += '<div class="info-box info">🔄 Refreshing inspection to verify cleared state...</div>';
                    setTimeout(inspectQRData, 1000);
                }, 2000);
                
                db.close();
                
            } catch (error) {
                enhancedLog(logLevels.ERROR, 'QR-Clear', 'QR data clear operation failed', {
                    error: error.message,
                    stack: error.stack
                });
                
                let html = '<div class="info-box error">';
                html += '<h4>❌ QR Data Clear Failed</h4>';
                html += `<p><strong>Error:</strong> ${error.message}</p>`;
                
                // Provide specific troubleshooting based on error type
                html += '<div style="margin-top: 15px;">';
                html += '<strong>🔧 Clear Operation Troubleshooting:</strong><br>';
                
                if (error.message.includes('not supported')) {
                    html += '• IndexedDB is not supported in your browser<br>';
                    html += '• Try using a modern browser (Chrome, Firefox, Safari, Edge)<br>';
                    html += '• Disable private/incognito mode if active<br>';
                } else if (error.message.includes('not initialized') || error.message.includes('not found')) {
                    html += '• The offline database does not exist yet<br>';
                    html += '• No data to clear - this is normal for new installations<br>';
                    html += '• Try creating test data first, then clearing it<br>';
                } else if (error.message.includes('blocked')) {
                    html += '• Close other tabs using this application<br>';
                    html += '• Refresh the page and try again<br>';
                    html += '• Wait a moment and retry the operation<br>';
                } else if (error.message.includes('Transaction')) {
                    html += '• Database transaction failed during clear operation<br>';
                    html += '• Try refreshing the page and attempting again<br>';
                    html += '• Check if other tabs are using the database<br>';
                } else {
                    html += '• Check the browser console for detailed error logs<br>';
                    html += '• Try refreshing the page and attempting again<br>';
                    html += '• Clear browser data if the problem persists<br>';
                }
                
                html += '• Contact support if the issue continues<br>';
                html += '</div>';
                html += '</div>';
                
                info.innerHTML = html;
            } finally {
                // Reset button state
                if (button) {
                    button.classList.remove('btn-loading');
                    button.disabled = false;
                    button.innerHTML = originalText;
                }
            }
        }
        
        // Placeholder functions for other features (to be implemented in other tasks)
        function checkOfflineData() {
            document.getElementById('indexeddb-info').innerHTML = '<div class="info-box info">This function will be implemented in future tasks</div>';
        }
        
        function clearOfflineData() {
            document.getElementById('indexeddb-info').innerHTML = '<div class="info-box info">This function will be implemented in future tasks</div>';
        }
        
        // Enhanced QR Data Simulation Functions
        async function simulateDriverScan() {
            const info = document.getElementById('qr-simulation-info');
            const button = event.target;
            
            try {
                // Add loading state to button
                button.classList.add('btn-loading');
                button.disabled = true;
                const originalText = button.innerHTML;
                button.innerHTML = '<span class="status-indicator status-pending"></span>Simulating...';
                
                info.innerHTML = '<div class="info-box warning">🚛 Simulating driver QR scan...</div>';
                
                enhancedLog(logLevels.INFO, 'QR-Simulation', 'Starting driver QR scan simulation');
                
                const db = await openIndexedDB();
                const transaction = db.transaction(['connectionQueue'], 'readwrite');
                const store = transaction.objectStore('connectionQueue');
                
                // Generate unique ID based on existing data
                const existingData = await getAllFromStore(db, 'connectionQueue');
                const nextId = existingData.length + 1;
                
                const driverConnection = {
                    id: nextId,
                    apiPayload: {
                        driver_qr_data: {
                            id: `DR-SIM${nextId.toString().padStart(3, '0')}`,
                            employee_id: `DR-SIM${nextId.toString().padStart(3, '0')}`,
                            driver_id: 200 + nextId,
                            generated_date: new Date().toISOString(),
                            type: "driver",
                            checksum: `sim${Date.now()}${nextId}`,
                            name: `Simulated Driver ${nextId}`
                        },
                        truck_qr_data: {
                            id: `TSIM${nextId.toString().padStart(3, '0')}`,
                            type: "truck",
                            number: `SIM-${nextId}`
                        },
                        action: "check_in"
                    },
                    syncMetadata: {
                        status: "pending",
                        priority: 4,
                        retryCount: 0,
                        maxRetries: 3,
                        timestamp: new Date().toISOString(),
                        scheduledSync: new Date().toISOString(),
                        dataIntegrity: true
                    },
                    status: "pending",
                    action: "check_in",
                    employeeId: `DR-SIM${nextId.toString().padStart(3, '0')}`,
                    truckId: `TSIM${nextId.toString().padStart(3, '0')}`,
                    priority: 4,
                    timestamp: new Date().toISOString()
                };
                
                store.add(driverConnection);
                
                transaction.oncomplete = () => {
                    db.close();
                    
                    let html = '<div class="info-box success">';
                    html += '<h4>✅ Driver QR Scan Simulated Successfully</h4>';
                    html += '<p>Created simulated driver connection data in offline storage</p>';
                    html += '<div style="margin-top: 15px;">';
                    html += '<strong>📊 Simulated Data Details:</strong><br>';
                    html += `• Driver ID: ${driverConnection.employeeId}<br>`;
                    html += `• Truck ID: ${driverConnection.truckId}<br>`;
                    html += `• Action: ${driverConnection.action}<br>`;
                    html += `• Status: ${driverConnection.status}<br>`;
                    html += `• Priority: ${driverConnection.priority}<br>`;
                    html += `• Timestamp: ${new Date(driverConnection.timestamp).toLocaleString()}`;
                    html += '</div>';
                    html += '<div style="margin-top: 15px;">';
                    html += '<strong>💡 Next Steps:</strong><br>';
                    html += '• Use "Inspect Stored QR Data" to view the simulated data<br>';
                    html += '• Use "Test Manual Sync" to simulate sync process<br>';
                    html += '• Use "Sync Offline Data" to send data to server';
                    html += '</div>';
                    html += '</div>';
                    
                    info.innerHTML = html;
                    
                    enhancedLog(logLevels.INFO, 'QR-Simulation', 'Driver QR scan simulation completed successfully', {
                        driverId: driverConnection.employeeId,
                        truckId: driverConnection.truckId,
                        action: driverConnection.action
                    });
                };
                
                transaction.onerror = () => {
                    db.close();
                    throw new Error('Failed to store simulated driver scan data');
                };
                
            } catch (error) {
                enhancedLog(logLevels.ERROR, 'QR-Simulation', 'Driver QR scan simulation failed', {
                    error: error.message,
                    stack: error.stack
                });
                
                let html = '<div class="info-box error">';
                html += '<h4>❌ Driver QR Scan Simulation Failed</h4>';
                html += `<p><strong>Error:</strong> ${error.message}</p>`;
                html += '<div style="margin-top: 15px;">';
                html += '<strong>🔧 Troubleshooting:</strong><br>';
                html += '• Check if IndexedDB is supported in your browser<br>';
                html += '• Try refreshing the page and attempting again<br>';
                html += '• Check browser console for detailed error logs';
                html += '</div>';
                html += '</div>';
                
                info.innerHTML = html;
            } finally {
                // Reset button state
                if (button) {
                    button.classList.remove('btn-loading');
                    button.disabled = false;
                    button.innerHTML = originalText;
                }
            }
        }
        
        async function simulateTruckScan() {
            const info = document.getElementById('qr-simulation-info');
            const button = event.target;
            
            try {
                // Add loading state to button
                button.classList.add('btn-loading');
                button.disabled = true;
                const originalText = button.innerHTML;
                button.innerHTML = '<span class="status-indicator status-pending"></span>Simulating...';
                
                info.innerHTML = '<div class="info-box warning">🚚 Simulating truck QR scan...</div>';
                
                enhancedLog(logLevels.INFO, 'QR-Simulation', 'Starting truck QR scan simulation');
                
                const db = await openIndexedDB();
                const transaction = db.transaction(['connectionQueue'], 'readwrite');
                const store = transaction.objectStore('connectionQueue');
                
                // Generate unique ID based on existing data
                const existingData = await getAllFromStore(db, 'connectionQueue');
                const nextId = existingData.length + 1;
                
                const truckConnection = {
                    id: nextId,
                    apiPayload: {
                        driver_qr_data: {
                            id: `DR-TRK${nextId.toString().padStart(3, '0')}`,
                            employee_id: `DR-TRK${nextId.toString().padStart(3, '0')}`,
                            driver_id: 300 + nextId,
                            generated_date: new Date().toISOString(),
                            type: "driver",
                            checksum: `trk${Date.now()}${nextId}`,
                            name: `Truck Driver ${nextId}`
                        },
                        truck_qr_data: {
                            id: `TTRK${nextId.toString().padStart(3, '0')}`,
                            type: "truck",
                            number: `TRK-${nextId}`,
                            capacity: "Standard"
                        },
                        action: "check_out"
                    },
                    syncMetadata: {
                        status: "pending",
                        priority: 3,
                        retryCount: 0,
                        maxRetries: 3,
                        timestamp: new Date().toISOString(),
                        scheduledSync: new Date().toISOString(),
                        dataIntegrity: true
                    },
                    status: "pending",
                    action: "check_out",
                    employeeId: `DR-TRK${nextId.toString().padStart(3, '0')}`,
                    truckId: `TTRK${nextId.toString().padStart(3, '0')}`,
                    priority: 3,
                    timestamp: new Date().toISOString()
                };
                
                store.add(truckConnection);
                
                transaction.oncomplete = () => {
                    db.close();
                    
                    let html = '<div class="info-box success">';
                    html += '<h4>✅ Truck QR Scan Simulated Successfully</h4>';
                    html += '<p>Created simulated truck connection data in offline storage</p>';
                    html += '<div style="margin-top: 15px;">';
                    html += '<strong>📊 Simulated Data Details:</strong><br>';
                    html += `• Driver ID: ${truckConnection.employeeId}<br>`;
                    html += `• Truck ID: ${truckConnection.truckId}<br>`;
                    html += `• Action: ${truckConnection.action}<br>`;
                    html += `• Status: ${truckConnection.status}<br>`;
                    html += `• Priority: ${truckConnection.priority}<br>`;
                    html += `• Timestamp: ${new Date(truckConnection.timestamp).toLocaleString()}`;
                    html += '</div>';
                    html += '<div style="margin-top: 15px;">';
                    html += '<strong>💡 Next Steps:</strong><br>';
                    html += '• Use "Inspect Stored QR Data" to view the simulated data<br>';
                    html += '• Use "Test Manual Sync" to simulate sync process<br>';
                    html += '• Use "Sync Offline Data" to send data to server';
                    html += '</div>';
                    html += '</div>';
                    
                    info.innerHTML = html;
                    
                    enhancedLog(logLevels.INFO, 'QR-Simulation', 'Truck QR scan simulation completed successfully', {
                        driverId: truckConnection.employeeId,
                        truckId: truckConnection.truckId,
                        action: truckConnection.action
                    });
                };
                
                transaction.onerror = () => {
                    db.close();
                    throw new Error('Failed to store simulated truck scan data');
                };
                
            } catch (error) {
                enhancedLog(logLevels.ERROR, 'QR-Simulation', 'Truck QR scan simulation failed', {
                    error: error.message,
                    stack: error.stack
                });
                
                let html = '<div class="info-box error">';
                html += '<h4>❌ Truck QR Scan Simulation Failed</h4>';
                html += `<p><strong>Error:</strong> ${error.message}</p>`;
                html += '<div style="margin-top: 15px;">';
                html += '<strong>🔧 Troubleshooting:</strong><br>';
                html += '• Check if IndexedDB is supported in your browser<br>';
                html += '• Try refreshing the page and attempting again<br>';
                html += '• Check browser console for detailed error logs';
                html += '</div>';
                html += '</div>';
                
                info.innerHTML = html;
            } finally {
                // Reset button state
                if (button) {
                    button.classList.remove('btn-loading');
                    button.disabled = false;
                    button.innerHTML = originalText;
                }
            }
        }
        
        // Enhanced Test Manual Sync Function (Requirements 5.1-5.4)
        async function testManualSync() {
            const info = document.getElementById('qr-simulation-info');
            const button = event.target;
            
            try {
                // Add loading state to button
                button.classList.add('btn-loading');
                button.disabled = true;
                const originalText = button.innerHTML;
                button.innerHTML = '<span class="status-indicator status-pending"></span>Testing...';
                
                info.innerHTML = '<div class="info-box warning">🔄 Testing manual sync process...</div>';
                
                enhancedLog(logLevels.INFO, 'Manual-Sync', 'Starting manual sync test');
                
                // Check online/offline state
                const isOnline = navigator.onLine;
                if (!isOnline) {
                    let html = '<div class="info-box error">';
                    html += '<h4>🌐 Cannot Perform Sync Test - System Offline</h4>';
                    html += '<p>Manual sync testing requires an internet connection</p>';
                    html += '<div style="margin-top: 15px;">';
                    html += '<strong>💡 To test sync functionality:</strong><br>';
                    html += '• Connect to the internet<br>';
                    html += '• Refresh the page<br>';
                    html += '• Try the manual sync test again';
                    html += '</div>';
                    html += '</div>';
                    
                    info.innerHTML = html;
                    enhancedLog(logLevels.WARN, 'Manual-Sync', 'Manual sync test aborted - system is offline');
                    return;
                }
                
                // Get stored QR data
                const db = await openIndexedDB();
                const connections = await getAllFromStore(db, 'connectionQueue');
                db.close();
                
                if (connections.length === 0) {
                    let html = '<div class="info-box info">';
                    html += '<h4>📭 No Data Available for Sync Test</h4>';
                    html += '<p>The offline storage is currently empty</p>';
                    html += '<div style="margin-top: 15px;">';
                    html += '<strong>💡 To create test data:</strong><br>';
                    html += '• Use "Simulate Driver QR Scan" button<br>';
                    html += '• Use "Simulate Truck QR Scan" button<br>';
                    html += '• Scan actual QR codes while offline in the PWA';
                    html += '</div>';
                    html += '</div>';
                    
                    info.innerHTML = html;
                    enhancedLog(logLevels.INFO, 'Manual-Sync', 'Manual sync test completed - no data to sync');
                    return;
                }
                
                // Simulate sync process without actually sending data
                let html = '<div class="info-box success">';
                html += '<h4>✅ Manual Sync Test Completed Successfully</h4>';
                html += `<p>Found ${connections.length} connection${connections.length > 1 ? 's' : ''} that would be synced</p>`;
                
                // Show breakdown of connections
                html += '<div style="margin-top: 15px;">';
                html += '<strong>📊 Sync Preview:</strong><br>';
                connections.forEach((conn, index) => {
                    const driverInfo = conn.apiPayload?.driver_qr_data;
                    const truckInfo = conn.apiPayload?.truck_qr_data;
                    html += `${index + 1}. ${conn.action} - Driver: ${driverInfo?.employee_id || 'N/A'}, Truck: ${truckInfo?.id || 'N/A'} (Priority: ${conn.priority})<br>`;
                });
                html += '</div>';
                
                html += '<div style="margin-top: 15px;">';
                html += '<strong>🔄 Sync Process Simulation:</strong><br>';
                html += `• Network Status: ${isOnline ? '🟢 Online' : '🔴 Offline'}<br>`;
                html += `• Items to Sync: ${connections.length}<br>`;
                html += `• Estimated API Calls: ${connections.length}<br>`;
                html += `• Data Integrity: ✅ All connections valid<br>`;
                html += '• Simulation Mode: ✅ No actual data sent to server';
                html += '</div>';
                
                html += '<div style="margin-top: 15px;">';
                html += '<strong>💡 Next Steps:</strong><br>';
                html += '• Use "Sync Offline Data" to perform actual sync<br>';
                html += '• Use "Inspect Stored QR Data" to view detailed connection data<br>';
                html += '• Note: This test does NOT send data to the server';
                html += '</div>';
                html += '</div>';
                
                info.innerHTML = html;
                
                enhancedLog(logLevels.INFO, 'Manual-Sync', 'Manual sync test completed successfully', {
                    connectionsFound: connections.length,
                    isOnline,
                    connectionTypes: connections.map(c => c.action)
                });
                
            } catch (error) {
                enhancedLog(logLevels.ERROR, 'Manual-Sync', 'Manual sync test failed', {
                    error: error.message,
                    stack: error.stack
                });
                
                let html = '<div class="info-box error">';
                html += '<h4>❌ Manual Sync Test Failed</h4>';
                html += `<p><strong>Error:</strong> ${error.message}</p>`;
                html += '<div style="margin-top: 15px;">';
                html += '<strong>🔧 Troubleshooting:</strong><br>';
                
                if (error.message.includes('not supported')) {
                    html += '• IndexedDB is not supported in your browser<br>';
                    html += '• Try using a modern browser (Chrome, Firefox, Safari, Edge)<br>';
                } else if (error.message.includes('not initialized') || error.message.includes('not found')) {
                    html += '• The offline database has not been created yet<br>';
                    html += '• Try creating test data using the simulation buttons<br>';
                } else {
                    html += '• Check the browser console for detailed error logs<br>';
                    html += '• Try refreshing the page and attempting again<br>';
                }
                
                html += '</div>';
                html += '</div>';
                
                info.innerHTML = html;
            } finally {
                // Reset button state
                if (button) {
                    button.classList.remove('btn-loading');
                    button.disabled = false;
                    button.innerHTML = originalText;
                }
            }
        }
        
        // Enhanced Real Sync Offline Data Function (Requirements 4.1-4.5)
        async function syncOfflineData() {
            const info = document.getElementById('qr-simulation-info');
            const button = event.target;
            
            try {
                // Add loading state to button
                button.classList.add('btn-loading');
                button.disabled = true;
                const originalText = button.innerHTML;
                button.innerHTML = '<span class="status-indicator status-pending"></span>Syncing...';
                
                info.innerHTML = '<div class="info-box warning">🔄 Syncing offline data to server...</div>';
                
                enhancedLog(logLevels.INFO, 'Real-Sync', 'Starting real sync operation');
                
                // Check online/offline state
                const isOnline = navigator.onLine;
                if (!isOnline) {
                    let html = '<div class="info-box error">';
                    html += '<h4>🌐 Cannot Sync Offline Data - No Internet Connection</h4>';
                    html += '<p>Real sync requires an active internet connection</p>';
                    html += '<div style="margin-top: 15px;">';
                    html += '<strong>💡 To sync offline data:</strong><br>';
                    html += '• Connect to the internet<br>';
                    html += '• Refresh the page<br>';
                    html += '• Try the sync operation again<br>';
                    html += '• Data will remain stored until successfully synced';
                    html += '</div>';
                    html += '</div>';
                    
                    info.innerHTML = html;
                    enhancedLog(logLevels.WARN, 'Real-Sync', 'Real sync aborted - system is offline');
                    return;
                }
                
                // Get stored QR data
                const db = await openIndexedDB();
                const connections = await getAllFromStore(db, 'connectionQueue');
                
                if (connections.length === 0) {
                    db.close();
                    
                    let html = '<div class="info-box info">';
                    html += '<h4>📭 No Data to Sync</h4>';
                    html += '<p>The offline storage is currently empty</p>';
                    html += '<div style="margin-top: 15px;">';
                    html += '<strong>💡 To create data for syncing:</strong><br>';
                    html += '• Use "Simulate Driver QR Scan" button<br>';
                    html += '• Use "Simulate Truck QR Scan" button<br>';
                    html += '• Scan actual QR codes while offline in the PWA';
                    html += '</div>';
                    html += '</div>';
                    
                    info.innerHTML = html;
                    enhancedLog(logLevels.INFO, 'Real-Sync', 'Real sync completed - no data to sync');
                    return;
                }
                
                enhancedLog(logLevels.INFO, 'Real-Sync', `Found ${connections.length} connections to sync`);
                
                // Perform actual API calls to sync data
                let syncResults = {
                    successful: 0,
                    failed: 0,
                    errors: []
                };
                
                for (let i = 0; i < connections.length; i++) {
                    const connection = connections[i];
                    
                    try {
                        // Update progress
                        info.innerHTML = `<div class="info-box warning">🔄 Syncing connection ${i + 1} of ${connections.length}...</div>`;
                        
                        // Make API call to /api/driver/connect endpoint
                        const response = await fetch('/api/driver/connect', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
                            },
                            body: JSON.stringify(connection.apiPayload)
                        });
                        
                        if (response.ok) {
                            // Successful sync - remove from IndexedDB
                            const transaction = db.transaction(['connectionQueue'], 'readwrite');
                            const store = transaction.objectStore('connectionQueue');
                            store.delete(connection.id);
                            
                            await new Promise((resolve, reject) => {
                                transaction.oncomplete = resolve;
                                transaction.onerror = reject;
                            });
                            
                            syncResults.successful++;
                            enhancedLog(logLevels.INFO, 'Real-Sync', `Successfully synced connection ${connection.id}`);
                            
                        } else {
                            // Failed sync - keep data in IndexedDB
                            const errorText = await response.text();
                            syncResults.failed++;
                            syncResults.errors.push(`Connection ${connection.id}: ${response.status} ${errorText}`);
                            enhancedLog(logLevels.ERROR, 'Real-Sync', `Failed to sync connection ${connection.id}`, {
                                status: response.status,
                                error: errorText
                            });
                        }
                        
                    } catch (networkError) {
                        // Network error - keep data in IndexedDB
                        syncResults.failed++;
                        syncResults.errors.push(`Connection ${connection.id}: ${networkError.message}`);
                        enhancedLog(logLevels.ERROR, 'Real-Sync', `Network error syncing connection ${connection.id}`, {
                            error: networkError.message
                        });
                    }
                }
                
                db.close();
                
                // Display sync results
                let html = '';
                if (syncResults.successful > 0 && syncResults.failed === 0) {
                    html = '<div class="info-box success">';
                    html += '<h4>✅ Sync Completed Successfully</h4>';
                    html += `<p>All ${syncResults.successful} connection${syncResults.successful > 1 ? 's' : ''} synced successfully</p>`;
                } else if (syncResults.successful > 0 && syncResults.failed > 0) {
                    html = '<div class="info-box warning">';
                    html += '<h4>⚠️ Sync Partially Completed</h4>';
                    html += `<p>Successfully synced: ${syncResults.successful}, Failed: ${syncResults.failed}</p>`;
                } else {
                    html = '<div class="info-box error">';
                    html += '<h4>❌ Sync Failed</h4>';
                    html += `<p>Failed to sync all ${syncResults.failed} connection${syncResults.failed > 1 ? 's' : ''}</p>`;
                }
                
                html += '<div style="margin-top: 15px;">';
                html += '<strong>📊 Sync Results:</strong><br>';
                html += `• Successful: ${syncResults.successful}<br>`;
                html += `• Failed: ${syncResults.failed}<br>`;
                html += `• Total Processed: ${connections.length}<br>`;
                html += `• API Endpoint: /api/driver/connect<br>`;
                html += `• Completed: ${new Date().toLocaleString()}`;
                html += '</div>';
                
                if (syncResults.errors.length > 0) {
                    html += '<div style="margin-top: 15px;">';
                    html += '<strong>❌ Error Details:</strong><br>';
                    syncResults.errors.forEach(error => {
                        html += `• ${error}<br>`;
                    });
                    html += '</div>';
                }
                
                html += '<div style="margin-top: 15px;">';
                html += '<strong>💡 Next Steps:</strong><br>';
                if (syncResults.successful > 0) {
                    html += '• Successfully synced data has been removed from offline storage<br>';
                }
                if (syncResults.failed > 0) {
                    html += '• Failed data remains in offline storage for retry<br>';
                    html += '• Check network connection and server status<br>';
                    html += '• Try syncing again when issues are resolved<br>';
                }
                html += '• Use "Inspect Stored QR Data" to view remaining data';
                html += '</div>';
                html += '</div>';
                
                info.innerHTML = html;
                
                enhancedLog(logLevels.INFO, 'Real-Sync', 'Real sync operation completed', {
                    successful: syncResults.successful,
                    failed: syncResults.failed,
                    totalProcessed: connections.length
                });
                
            } catch (error) {
                enhancedLog(logLevels.ERROR, 'Real-Sync', 'Real sync operation failed', {
                    error: error.message,
                    stack: error.stack
                });
                
                let html = '<div class="info-box error">';
                html += '<h4>❌ Sync Operation Failed</h4>';
                html += `<p><strong>Error:</strong> ${error.message}</p>`;
                html += '<div style="margin-top: 15px;">';
                html += '<strong>🔧 Troubleshooting:</strong><br>';
                
                if (error.message.includes('not supported')) {
                    html += '• IndexedDB is not supported in your browser<br>';
                    html += '• Try using a modern browser (Chrome, Firefox, Safari, Edge)<br>';
                } else if (error.message.includes('not initialized') || error.message.includes('not found')) {
                    html += '• The offline database has not been created yet<br>';
                    html += '• Try creating test data using the simulation buttons<br>';
                } else if (error.message.includes('fetch')) {
                    html += '• Network connection issue or server unavailable<br>';
                    html += '• Check internet connection and try again<br>';
                    html += '• Verify server is running and accessible<br>';
                } else {
                    html += '• Check the browser console for detailed error logs<br>';
                    html += '• Try refreshing the page and attempting again<br>';
                    html += '• Contact support if the issue persists<br>';
                }
                
                html += '• Data remains safely stored offline until sync succeeds<br>';
                html += '</div>';
                html += '</div>';
                
                info.innerHTML = html;
            } finally {
                // Reset button state
                if (button) {
                    button.classList.remove('btn-loading');
                    button.disabled = false;
                    button.innerHTML = originalText;
                }
            }
        }
        
        // Function to update button status indicators
        function updateButtonStatus(buttonSelector, status) {
            const button = document.querySelector(buttonSelector);
            if (button) {
                const indicator = button.querySelector('.status-indicator');
                if (indicator) {
                    indicator.className = `status-indicator status-${status}`;
                }
            }
        }
        
        // Function to update all button statuses based on current state
        async function updateAllButtonStatuses() {
            try {
                // Check PWA mode
                const isPWAMode = window.matchMedia('(display-mode: standalone)').matches || 
                                 window.navigator.standalone === true;
                updateButtonStatus('button[onclick="testPWAMode()"]', isPWAMode ? 'online' : 'offline');
                
                // Check service worker
                if ('serviceWorker' in navigator) {
                    const registration = await navigator.serviceWorker.getRegistration();
                    updateButtonStatus('button[onclick="checkServiceWorker()"]', registration ? 'online' : 'offline');
                } else {
                    updateButtonStatus('button[onclick="checkServiceWorker()"]', 'offline');
                }
                
                // Check IndexedDB
                try {
                    const db = await openIndexedDB();
                    const connections = await getAllFromStore(db, 'connectionQueue');
                    updateButtonStatus('button[onclick="inspectQRData()"]', connections.length > 0 ? 'online' : 'unknown');
                    updateButtonStatus('button[onclick="clearQRData()"]', connections.length > 0 ? 'online' : 'offline');
                    updateButtonStatus('button[onclick="testManualSync()"]', connections.length > 0 ? 'online' : 'offline');
                    updateButtonStatus('button[onclick="syncOfflineData()"]', connections.length > 0 ? 'online' : 'offline');
                    db.close();
                } catch (error) {
                    updateButtonStatus('button[onclick="inspectQRData()"]', 'offline');
                    updateButtonStatus('button[onclick="clearQRData()"]', 'offline');
                }
                
                // Check network status
                const networkStatus = navigator.onLine ? 'online' : 'offline';
                updateButtonStatus('button[onclick="syncOfflineData()"]', networkStatus);
                
            } catch (error) {
                console.warn('Failed to update button statuses:', error);
            }
        }
        
        // Initialize debug page with error handling
        function initializeDebugPage() {
            try {
                console.log('[Debug] Initializing debug page...');
                
                // Auto-run environment check
                checkEnvironment();
                
                // Update button statuses on page load
                setTimeout(() => {
                    try {
                        updateAllButtonStatuses();
                    } catch (error) {
                        console.error('[Debug] Error updating button statuses:', error);
                    }
                }, 1000);
                
                console.log('[Debug] Debug page initialized successfully');
            } catch (error) {
                console.error('[Debug] Error initializing debug page:', error);
                // Show error message to user
                document.body.insertAdjacentHTML('afterbegin', 
                    '<div style="background: #f8d7da; color: #721c24; padding: 15px; margin: 10px; border-radius: 5px; border: 1px solid #f5c6cb;">' +
                    '<strong>⚠️ Debug Page Initialization Error:</strong> ' + error.message + 
                    '<br><small>Check browser console for details. Some features may not work properly.</small>' +
                    '</div>'
                );
            }
        }
        
        // Run initialization when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeDebugPage);
        } else {
            initializeDebugPage();
        }
        
        // Listen for network changes
        window.addEventListener('online', () => {
            console.log('Network: ONLINE');
            checkEnvironment();
            updateAllButtonStatuses();
        });
        
        window.addEventListener('offline', () => {
            console.log('Network: OFFLINE');
            checkEnvironment();
            updateAllButtonStatuses();
        });
        
        // Update button statuses when page becomes visible (user switches back to tab)
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                setTimeout(updateAllButtonStatuses, 500);
            }
        });
    </script>
</body>
</html>