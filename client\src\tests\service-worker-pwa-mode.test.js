/**
 * Service Worker PWA Mode Detection and Offline Content Serving Tests
 * 
 * This test suite validates that the service worker:
 * 1. Correctly receives and stores PWA mode status from clients
 * 2. Only serves cached content for driver-connect routes in PWA mode
 * 3. Allows normal failures for browser mode to show "site can't be reached"
 * 4. <PERSON>les PWA mode detection requests and responses properly
 * 
 * Requirements: 1.1, 1.2, 1.3, 1.4, 5.1, 5.2, 5.3, 5.4
 */

// Mock service worker environment
global.self = {
  addEventListener: jest.fn(),
  clients: {
    matchAll: jest.fn(),
    claim: jest.fn()
  },
  registration: {
    scope: '/'
  },
  skipWaiting: jest.fn(),
  caches: {
    open: jest.fn(),
    match: jest.fn(),
    keys: jest.fn(),
    delete: jest.fn()
  },
  fetch: jest.fn(),
  navigator: {
    onLine: true
  }
};

global.caches = {
  open: jest.fn(),
  match: jest.fn(),
  keys: jest.fn(),
  delete: jest.fn()
};

// Mock fetch
global.fetch = jest.fn();

// Mock console methods
global.console = {
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn()
};

describe('Service Worker PWA Mode Detection Tests', () => {
  let serviceWorkerCode;
  let messageHandler;
  let fetchHandler;
  let mockCache;
  let mockClients;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup mock cache
    mockCache = {
      match: jest.fn(),
      put: jest.fn(),
      addAll: jest.fn(),
      add: jest.fn()
    };

    global.caches.open.mockResolvedValue(mockCache);
    global.caches.match.mockResolvedValue(null);

    // Setup mock clients
    mockClients = [
      {
        postMessage: jest.fn(),
        url: 'https://example.com/driver-connect'
      }
    ];

    global.self.clients.matchAll.mockResolvedValue(mockClients);

    // Mock fetch to simulate network failures
    global.fetch.mockRejectedValue(new Error('Network error'));

    // Simulate service worker code execution
    // We'll test the key functions that would be defined in the service worker
    
    // Mock PWA mode detection variables (these would be global in the actual SW)
    global.clientPWAMode = false;
    global.pwaStatusLastUpdated = null;

    // Create mock handlers that simulate the service worker behavior
    messageHandler = jest.fn((event) => {
      if (event.data && event.data.type === 'PWA_MODE_STATUS') {
        global.clientPWAMode = event.data.isPWA;
        global.pwaStatusLastUpdated = Date.now();
        
        if (event.source) {
          event.source.postMessage({
            type: 'PWA_MODE_CONFIRMATION',
            confirmedMode: global.clientPWAMode,
            timestamp: new Date().toISOString(),
            source: 'service-worker'
          });
        }
      }
    });

    fetchHandler = jest.fn(async (event) => {
      const request = event.request;
      const url = new URL(request.url);

      // Simulate navigation request handling
      if (request.mode === 'navigate') {
        return await handleNavigation(request);
      }

      // Let other requests pass through
      return fetch(request);
    });

    // Mock navigation handler
    global.handleNavigation = async (request) => {
      const url = new URL(request.url);
      
      // Simulate PWA mode detection
      const isPWAMode = global.clientPWAMode;
      
      // Check if this is a driver-connect route
      const isDriverConnectRoute = url.pathname.startsWith('/driver-connect');
      
      if (isDriverConnectRoute) {
        if (isPWAMode) {
          // PWA mode - serve cached content
          const cachedResponse = await global.caches.match('/');
          if (cachedResponse) {
            return cachedResponse;
          }
          
          // Fallback to offline page
          const offlineFallback = await global.caches.match('/offline-fallback.html');
          if (offlineFallback) {
            return offlineFallback;
          }
        } else {
          // Browser mode - let it fail normally
          throw new Error('Network error - site can\'t be reached');
        }
      }
      
      // For other routes, try to serve cached content regardless of PWA mode
      const cachedResponse = await global.caches.match('/');
      if (cachedResponse) {
        return cachedResponse;
      }
      
      throw new Error('Network error');
    };

    // Mock detectPWAMode function
    global.detectPWAMode = async () => {
      // Use client-reported status if available and recent
      const now = Date.now();
      if (global.pwaStatusLastUpdated && (now - global.pwaStatusLastUpdated) < 30000) {
        return global.clientPWAMode;
      }

      // Request fresh status from clients
      const clients = await global.self.clients.matchAll();
      if (clients.length > 0) {
        clients[0].postMessage({
          type: 'REQUEST_PWA_MODE',
          timestamp: new Date().toISOString()
        });
        
        // Simulate waiting for response
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve(global.clientPWAMode);
          }, 100);
        });
      }
      
      return false;
    };
  });

  describe('PWA Mode Status Reception and Storage', () => {
    test('Should receive and store PWA mode status from client', () => {
      const mockEvent = {
        data: {
          type: 'PWA_MODE_STATUS',
          isPWA: true,
          currentPath: '/driver-connect',
          timestamp: new Date().toISOString(),
          source: 'pwa-status-hook'
        },
        source: {
          postMessage: jest.fn()
        }
      };

      messageHandler(mockEvent);

      expect(global.clientPWAMode).toBe(true);
      expect(global.pwaStatusLastUpdated).toBeTruthy();
      expect(mockEvent.source.postMessage).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'PWA_MODE_CONFIRMATION',
          confirmedMode: true,
          source: 'service-worker'
        })
      );
    });

    test('Should update PWA mode when status changes', () => {
      // First set to PWA mode
      const pwaEvent = {
        data: {
          type: 'PWA_MODE_STATUS',
          isPWA: true
        },
        source: { postMessage: jest.fn() }
      };

      messageHandler(pwaEvent);
      expect(global.clientPWAMode).toBe(true);

      // Then change to browser mode
      const browserEvent = {
        data: {
          type: 'PWA_MODE_STATUS',
          isPWA: false
        },
        source: { postMessage: jest.fn() }
      };

      messageHandler(browserEvent);
      expect(global.clientPWAMode).toBe(false);
    });

    test('Should handle location change notifications', () => {
      const mockEvent = {
        data: {
          type: 'LOCATION_CHANGE',
          currentPath: '/driver-connect',
          isPWA: true,
          timestamp: new Date().toISOString(),
          source: 'pwa-status-hook'
        }
      };

      messageHandler(mockEvent);

      expect(global.clientPWAMode).toBe(true);
      expect(global.pwaStatusLastUpdated).toBeTruthy();
    });
  });

  describe('PWA Mode Detection Function', () => {
    test('Should use cached client-reported status when recent', async () => {
      // Set recent PWA status
      global.clientPWAMode = true;
      global.pwaStatusLastUpdated = Date.now() - 10000; // 10 seconds ago

      const result = await global.detectPWAMode();

      expect(result).toBe(true);
      expect(global.self.clients.matchAll).not.toHaveBeenCalled();
    });

    test('Should request fresh status when cached status is stale', async () => {
      // Set stale PWA status
      global.clientPWAMode = true;
      global.pwaStatusLastUpdated = Date.now() - 60000; // 1 minute ago

      const result = await global.detectPWAMode();

      expect(global.self.clients.matchAll).toHaveBeenCalled();
      expect(mockClients[0].postMessage).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'REQUEST_PWA_MODE'
        })
      );
    });

    test('Should default to false when no clients available', async () => {
      global.self.clients.matchAll.mockResolvedValue([]);

      const result = await global.detectPWAMode();

      expect(result).toBe(false);
    });
  });

  describe('Navigation Request Handling', () => {
    test('Should serve cached content for driver-connect routes in PWA mode', async () => {
      // Set PWA mode
      global.clientPWAMode = true;
      global.pwaStatusLastUpdated = Date.now();

      // Mock cached response
      const mockCachedResponse = new Response('<html>Cached PWA content</html>');
      global.caches.match.mockResolvedValue(mockCachedResponse);

      const mockRequest = {
        url: 'https://example.com/driver-connect',
        mode: 'navigate'
      };

      const result = await global.handleNavigation(mockRequest);

      expect(result).toBe(mockCachedResponse);
      expect(global.caches.match).toHaveBeenCalledWith('/');
    });

    test('Should allow normal failure for driver-connect routes in browser mode', async () => {
      // Set browser mode
      global.clientPWAMode = false;
      global.pwaStatusLastUpdated = Date.now();

      const mockRequest = {
        url: 'https://example.com/driver-connect',
        mode: 'navigate'
      };

      await expect(global.handleNavigation(mockRequest)).rejects.toThrow('Network error - site can\'t be reached');
    });

    test('Should serve cached content for non-driver-connect routes regardless of PWA mode', async () => {
      // Set browser mode
      global.clientPWAMode = false;
      global.pwaStatusLastUpdated = Date.now();

      // Mock cached response
      const mockCachedResponse = new Response('<html>Cached content</html>');
      global.caches.match.mockResolvedValue(mockCachedResponse);

      const mockRequest = {
        url: 'https://example.com/trip-scanner',
        mode: 'navigate'
      };

      const result = await global.handleNavigation(mockRequest);

      expect(result).toBe(mockCachedResponse);
    });

    test('Should serve offline fallback when main cache not available in PWA mode', async () => {
      // Set PWA mode
      global.clientPWAMode = true;
      global.pwaStatusLastUpdated = Date.now();

      // Mock no main cache but offline fallback available
      const mockOfflineFallback = new Response('<html>Offline fallback</html>');
      global.caches.match
        .mockResolvedValueOnce(null) // No main cache
        .mockResolvedValueOnce(mockOfflineFallback); // Offline fallback available

      const mockRequest = {
        url: 'https://example.com/driver-connect',
        mode: 'navigate'
      };

      const result = await global.handleNavigation(mockRequest);

      expect(result).toBe(mockOfflineFallback);
      expect(global.caches.match).toHaveBeenCalledWith('/');
      expect(global.caches.match).toHaveBeenCalledWith('/offline-fallback.html');
    });
  });

  describe('Service Worker Message Handling', () => {
    test('Should handle client ready notifications', () => {
      const mockEvent = {
        data: {
          type: 'CLIENT_READY',
          timestamp: new Date().toISOString(),
          source: 'pwa-status-hook'
        },
        source: {
          postMessage: jest.fn()
        }
      };

      messageHandler(mockEvent);

      expect(mockEvent.source.postMessage).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'SERVICE_WORKER_READY',
          currentPWAMode: expect.any(Boolean),
          source: 'service-worker'
        })
      );
    });

    test('Should handle PWA mode requests from service worker', async () => {
      const mockEvent = {
        data: {
          type: 'REQUEST_PWA_MODE',
          timestamp: new Date().toISOString(),
          requestId: 'test-123'
        }
      };

      // This would trigger the client to send PWA_MODE_STATUS
      // We simulate this by calling the message handler
      expect(() => messageHandler(mockEvent)).not.toThrow();
    });

    test('Should ignore unknown message types', () => {
      const mockEvent = {
        data: {
          type: 'UNKNOWN_MESSAGE_TYPE',
          someData: 'test'
        }
      };

      expect(() => messageHandler(mockEvent)).not.toThrow();
    });

    test('Should handle malformed messages gracefully', () => {
      const malformedEvents = [
        { data: null },
        { data: undefined },
        { data: 'string instead of object' },
        { data: { type: null } },
        {}
      ];

      malformedEvents.forEach(event => {
        expect(() => messageHandler(event)).not.toThrow();
      });
    });
  });

  describe('Integration Tests', () => {
    test('Should maintain PWA mode consistency across multiple messages', () => {
      // Initial PWA mode
      const pwaEvent = {
        data: {
          type: 'PWA_MODE_STATUS',
          isPWA: true
        },
        source: { postMessage: jest.fn() }
      };

      messageHandler(pwaEvent);
      expect(global.clientPWAMode).toBe(true);

      // Location change should maintain PWA mode
      const locationEvent = {
        data: {
          type: 'LOCATION_CHANGE',
          currentPath: '/driver-connect',
          isPWA: true
        }
      };

      messageHandler(locationEvent);
      expect(global.clientPWAMode).toBe(true);

      // Client ready should not change PWA mode
      const readyEvent = {
        data: {
          type: 'CLIENT_READY'
        },
        source: { postMessage: jest.fn() }
      };

      messageHandler(readyEvent);
      expect(global.clientPWAMode).toBe(true);
    });

    test('Should handle rapid PWA mode changes', () => {
      const events = [
        { data: { type: 'PWA_MODE_STATUS', isPWA: true }, source: { postMessage: jest.fn() } },
        { data: { type: 'PWA_MODE_STATUS', isPWA: false }, source: { postMessage: jest.fn() } },
        { data: { type: 'PWA_MODE_STATUS', isPWA: true }, source: { postMessage: jest.fn() } },
        { data: { type: 'PWA_MODE_STATUS', isPWA: false }, source: { postMessage: jest.fn() } }
      ];

      events.forEach((event, index) => {
        messageHandler(event);
        expect(global.clientPWAMode).toBe(event.data.isPWA);
      });
    });

    test('Should handle concurrent navigation requests correctly', async () => {
      // Set PWA mode
      global.clientPWAMode = true;
      global.pwaStatusLastUpdated = Date.now();

      const mockCachedResponse = new Response('<html>Cached content</html>');
      global.caches.match.mockResolvedValue(mockCachedResponse);

      const requests = [
        { url: 'https://example.com/driver-connect', mode: 'navigate' },
        { url: 'https://example.com/driver-connect/scan', mode: 'navigate' },
        { url: 'https://example.com/trip-scanner', mode: 'navigate' }
      ];

      const results = await Promise.all(
        requests.map(request => global.handleNavigation(request))
      );

      // All should return cached content in PWA mode
      results.forEach(result => {
        expect(result).toBe(mockCachedResponse);
      });
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('Should handle cache access errors gracefully', async () => {
      global.clientPWAMode = true;
      global.pwaStatusLastUpdated = Date.now();

      // Mock cache error
      global.caches.match.mockRejectedValue(new Error('Cache access failed'));

      const mockRequest = {
        url: 'https://example.com/driver-connect',
        mode: 'navigate'
      };

      await expect(global.handleNavigation(mockRequest)).rejects.toThrow();
    });

    test('Should handle client communication errors', async () => {
      global.self.clients.matchAll.mockRejectedValue(new Error('Client access failed'));

      const result = await global.detectPWAMode();

      expect(result).toBe(false); // Should default to false on error
    });

    test('Should handle timestamp edge cases', () => {
      // Test with invalid timestamp
      global.pwaStatusLastUpdated = 'invalid';
      
      expect(() => {
        const event = {
          data: { type: 'PWA_MODE_STATUS', isPWA: true },
          source: { postMessage: jest.fn() }
        };
        messageHandler(event);
      }).not.toThrow();

      expect(global.pwaStatusLastUpdated).toBeGreaterThan(0);
    });

    test('Should handle missing event source gracefully', () => {
      const eventWithoutSource = {
        data: {
          type: 'PWA_MODE_STATUS',
          isPWA: true
        }
        // No source property
      };

      expect(() => messageHandler(eventWithoutSource)).not.toThrow();
      expect(global.clientPWAMode).toBe(true);
    });
  });
});