// Driver Connect Offline Service
// Handles offline storage and synchronization for driver connections

import { offlineDB, SYNC_STATUS, PRIORITY } from './offlineDB.js';

// Driver connect specific data structures and validation
export class DriverConnectOfflineService {
  constructor() {
    this.storeName = 'connectionQueue';
  }

  // Store driver connection data offline in exact API format for perfect sync compatibility
  async storeConnection(connectionData) {
    try {
      // Ensure database is initialized
      await offlineDB.initialize();
      if (!offlineDB.db) {
        throw new Error('Database not initialized');
      }

      // Validate connection data
      const validatedData = this.validateConnectionData(connectionData);
      if (!validatedData.isValid) {
        throw new Error(`Invalid connection data: ${validatedData.errors.join(', ')}`);
      }

      // Create API payload in exact format expected by /api/driver/connect
      const apiPayload = this.createAPIPayload(validatedData);

      // Create offline connection record with exact API format
      const offlineConnection = {
        // EXACT API PAYLOAD - Direct pass-through to /api/driver/connect
        apiPayload: apiPayload,

        // Sync metadata for offline management
        syncMetadata: {
          storedAt: new Date().toISOString(),
          priority: this.determinePriority(validatedData),
          retryCount: 0,
          lastSyncAttempt: null,
          syncErrors: []
        },

        // Quick access fields for IndexedDB queries (derived from payload)
        status: SYNC_STATUS.PENDING, // Add status at root level for IndexedDB indexing
        action: apiPayload.action,
        employeeId: apiPayload.driver_qr_data?.employee_id || 'unknown',
        truckId: apiPayload.truck_qr_data?.id || null,
        priority: this.determinePriority(validatedData), // Add priority at root level for IndexedDB indexing
        timestamp: new Date().toISOString(), // Add timestamp at root level for IndexedDB indexing

        // Device context for audit
        deviceInfo: this.getDeviceInfo()
      };

      // Store in IndexedDB
      const id = await offlineDB.addData(this.storeName, offlineConnection);

      console.log('[DriverConnectOffline] Connection stored offline (API-compatible format):', {
        id,
        action: offlineConnection.action,
        employeeId: offlineConnection.employeeId,
        truckId: offlineConnection.truckId,
        priority: offlineConnection.syncMetadata.priority,
        apiPayloadReady: true
      });

      return {
        success: true,
        id,
        message: '📱 Connection saved offline - will sync when connected',
        offlineMode: true,
        priority: offlineConnection.syncMetadata.priority
      };

    } catch (error) {
      console.error('[DriverConnectOffline] Failed to store connection:', error);
      throw new Error(`Failed to store connection offline: ${error.message}`);
    }
  }

  // Validate connection data structure
  validateConnectionData(data) {
    const errors = [];
    
    // Check required fields
    if (!data.action) {
      errors.push('Action is required');
    }

    if (!data.driver_qr_data || !data.driver_qr_data.employee_id) {
      errors.push('Driver QR data with employee_id is required');
    }

    if (!data.truck_qr_data || !data.truck_qr_data.id) {
      errors.push('Truck QR data with id is required');
    }

    // Validate action type
    const validActions = ['check_in', 'check_out', 'break_start', 'break_end'];
    if (data.action && !validActions.includes(data.action)) {
      errors.push(`Invalid action: ${data.action}. Must be one of: ${validActions.join(', ')}`);
    }

    return {
      isValid: errors.length === 0,
      errors: errors,
      data: data
    };
  }

  // Create API payload in exact format expected by server
  createAPIPayload(validatedData) {
    return {
      action: validatedData.data.action,
      driver_qr_data: {
        employee_id: validatedData.data.driver_qr_data.employee_id,
        name: validatedData.data.driver_qr_data.name || '',
        department: validatedData.data.driver_qr_data.department || '',
        // Include any additional driver fields
        ...validatedData.data.driver_qr_data
      },
      truck_qr_data: {
        id: validatedData.data.truck_qr_data.id,
        number: validatedData.data.truck_qr_data.number || '',
        license_plate: validatedData.data.truck_qr_data.license_plate || '',
        // Include any additional truck fields
        ...validatedData.data.truck_qr_data
      },
      timestamp: validatedData.data.timestamp || new Date().toISOString(),
      ip_address: this.getClientIP(),
      user_agent: this.getUserAgent(),
      // Include any additional metadata
      metadata: {
        offline_stored: true,
        stored_at: new Date().toISOString(),
        ...validatedData.data.metadata
      }
    };
  }

  // Determine sync priority based on action type
  determinePriority(validatedData) {
    const action = validatedData.data.action;
    
    // High priority for check-in/check-out operations
    if (action === 'check_in' || action === 'check_out') {
      return PRIORITY.HIGH;
    }
    
    // Medium priority for break operations
    if (action === 'break_start' || action === 'break_end') {
      return PRIORITY.MEDIUM;
    }
    
    return PRIORITY.LOW;
  }

  // Get client IP (placeholder for offline mode)
  getClientIP() {
    // In browser environment, we can't directly get IP
    // This will be handled by the server during sync
    return 'offline_client';
  }

  // Get user agent string
  getUserAgent() {
    return navigator.userAgent || 'PWA_Offline_Client';
  }

  // Get device information for audit trail
  getDeviceInfo() {
    return {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      timestamp: new Date().toISOString()
    };
  }

  // Get all pending connections
  async getPendingConnections() {
    try {
      const pendingConnections = await offlineDB.getAllPending(this.storeName);
      
      // Sort by priority and timestamp
      return pendingConnections.sort((a, b) => {
        // First sort by priority (high > medium > low)
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
        if (priorityDiff !== 0) return priorityDiff;
        
        // Then sort by timestamp (older first)
        return new Date(a.timestamp) - new Date(b.timestamp);
      });
      
    } catch (error) {
      console.error('[DriverConnectOffline] Failed to get pending connections:', error);
      return [];
    }
  }

  // Update connection status
  async updateConnectionStatus(id, status, additionalData = {}) {
    try {
      // Get current connection data
      const transaction = offlineDB.db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      const connection = await new Promise((resolve, reject) => {
        const request = store.get(id);
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
      });

      if (!connection) {
        throw new Error(`Connection ${id} not found`);
      }

      // Update connection with new status and additional data
      const updatedConnection = {
        ...connection,
        status, // Update root level status for IndexedDB indexing
        syncMetadata: {
          ...connection.syncMetadata,
          lastSyncAttempt: new Date().toISOString(),
          ...additionalData
        }
      };

      await offlineDB.updateData(this.storeName, updatedConnection);
      
      console.log(`[DriverConnectOffline] Updated connection ${id} status to ${status}`);
      return updatedConnection;
      
    } catch (error) {
      console.error('[DriverConnectOffline] Failed to update connection status:', error);
      throw error;
    }
  }

  // Remove synced connection
  async removeSyncedConnection(id) {
    try {
      await offlineDB.deleteData(this.storeName, id);
      console.log(`[DriverConnectOffline] Removed synced connection: ${id}`);
    } catch (error) {
      console.error('[DriverConnectOffline] Failed to remove synced connection:', error);
      throw error;
    }
  }

  // Get pending connection count
  async getPendingCount() {
    try {
      const pendingConnections = await offlineDB.getDataByIndex(this.storeName, 'status', SYNC_STATUS.PENDING);
      return pendingConnections.length;
    } catch (error) {
      console.error('[DriverConnectOffline] Failed to get pending count:', error);
      return 0;
    }
  }

  // Get connection statistics
  async getStats() {
    try {
      const allConnections = await offlineDB.getDataByIndex(this.storeName, 'status', SYNC_STATUS.PENDING);
      const failedConnections = await offlineDB.getDataByIndex(this.storeName, 'status', SYNC_STATUS.FAILED);

      const stats = {
        total: allConnections.length + failedConnections.length,
        pending: allConnections.length,
        failed: failedConnections.length,
        byPriority: {
          high: allConnections.filter(c => c.priority === PRIORITY.HIGH).length,
          medium: allConnections.filter(c => c.priority === PRIORITY.MEDIUM).length,
          low: allConnections.filter(c => c.priority === PRIORITY.LOW).length
        },
        byAction: {}
      };

      // Count by action type
      allConnections.forEach(connection => {
        const action = connection.action || 'unknown';
        stats.byAction[action] = (stats.byAction[action] || 0) + 1;
      });

      return stats;

    } catch (error) {
      console.error('[DriverConnectOffline] Failed to get stats:', error);
      return { total: 0, pending: 0, failed: 0, byPriority: {}, byAction: {} };
    }
  }

  // Get failed connections for recovery
  async getFailedConnections() {
    try {
      await offlineDB.initialize();
      const failedConnections = await offlineDB.getDataByIndex(this.storeName, 'status', SYNC_STATUS.FAILED);
      console.log(`[DriverConnectOffline] Found ${failedConnections.length} failed connections`);
      return failedConnections;
    } catch (error) {
      console.error('[DriverConnectOffline] Error getting failed connections:', error);
      throw error;
    }
  }

  // Clear corrupted connections that might cause sync errors
  async clearCorruptedConnections() {
    try {
      await offlineDB.initialize();
      
      const allConnections = await offlineDB.getAllData(this.storeName);
      let corruptedCount = 0;
      let clearedCount = 0;
      
      for (const connection of allConnections) {
        try {
          // Check if connection data is corrupted
          const validation = this.validateConnectionData(connection);
          
          if (!validation.isValid) {
            // Connection is corrupted, remove it
            await offlineDB.deleteData(this.storeName, connection.id);
            corruptedCount++;
            clearedCount++;
            console.log(`[DriverConnectOffline] Cleared corrupted connection ${connection.id}:`, validation.errors);
          }
        } catch (error) {
          // If we can't even validate it, it's definitely corrupted
          try {
            await offlineDB.deleteData(this.storeName, connection.id);
            corruptedCount++;
            clearedCount++;
            console.log(`[DriverConnectOffline] Cleared severely corrupted connection ${connection.id}`);
          } catch (deleteError) {
            console.error(`[DriverConnectOffline] Failed to delete corrupted connection ${connection.id}:`, deleteError);
          }
        }
      }
      
      const result = {
        total: allConnections.length,
        corrupted: corruptedCount,
        cleared: clearedCount,
        success: true
      };
      
      console.log('[DriverConnectOffline] Corrupted data cleanup completed:', result);
      return result;
      
    } catch (error) {
      console.error('[DriverConnectOffline] Error clearing corrupted connections:', error);
      return {
        total: 0,
        corrupted: 0,
        cleared: 0,
        success: false,
        error: error.message
      };
    }
  }

  // Clear all connection data (for testing/debugging)
  async clearAllConnections() {
    try {
      await offlineDB.initialize();
      if (!offlineDB.db) {
        throw new Error('Database not initialized');
      }

      const transaction = offlineDB.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      
      await new Promise((resolve, reject) => {
        const request = store.clear();
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      console.log('[DriverConnectOffline] All connection data cleared');
    } catch (error) {
      console.error('[DriverConnectOffline] Failed to clear connection data:', error);
      throw error;
    }
  }
}

// Create singleton instance
export const driverConnectOffline = new DriverConnectOfflineService();

// Export service instance
export default driverConnectOffline;