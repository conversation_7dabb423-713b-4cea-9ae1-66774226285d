// Test Script for PWA Sync Error Fixes
// Run this in the browser console to test the sync error recovery functionality

console.log('=== PWA Sync Error Fix Verification ===');

// Test 1: Check if enhanced background sync methods exist
async function testBackgroundSyncEnhancements() {
  console.log('\n1. Testing Background Sync Enhancements...');
  
  try {
    // Import background sync service (this would work in the actual app context)
    if (typeof window !== 'undefined' && window.backgroundSync) {
      const bgSync = window.backgroundSync;
      
      // Test validation method
      if (typeof bgSync.validateConnectionData === 'function') {
        console.log('✅ validateConnectionData method exists');
        
        // Test with valid data
        const validData = {
          id: 'test-123',
          apiPayload: {
            action: 'check_in',
            driver_qr_data: { employee_id: 'EMP001' },
            truck_qr_data: { id: 'TRUCK001' },
            timestamp: new Date().toISOString()
          }
        };
        
        const validResult = bgSync.validateConnectionData(validData);
        console.log('✅ Valid data validation:', validResult);
        
        // Test with invalid data
        const invalidData = { id: 'test-456' };
        const invalidResult = bgSync.validateConnectionData(invalidData);
        console.log('✅ Invalid data validation:', invalidResult);
        
      } else {
        console.log('❌ validateConnectionData method not found');
      }
      
      // Test recovery methods
      if (typeof bgSync.recoverFailedConnections === 'function') {
        console.log('✅ recoverFailedConnections method exists');
      } else {
        console.log('❌ recoverFailedConnections method not found');
      }
      
      if (typeof bgSync.clearCorruptedData === 'function') {
        console.log('✅ clearCorruptedData method exists');
      } else {
        console.log('❌ clearCorruptedData method not found');
      }
      
    } else {
      console.log('❌ Background sync service not available in global scope');
    }
    
  } catch (error) {
    console.error('❌ Background sync test failed:', error);
  }
}

// Test 2: Check if driver connect offline service has new methods
async function testDriverConnectOfflineEnhancements() {
  console.log('\n2. Testing Driver Connect Offline Enhancements...');
  
  try {
    // This would work in the actual app context
    if (typeof window !== 'undefined' && window.driverConnectOffline) {
      const dcOffline = window.driverConnectOffline;
      
      if (typeof dcOffline.getFailedConnections === 'function') {
        console.log('✅ getFailedConnections method exists');
      } else {
        console.log('❌ getFailedConnections method not found');
      }
      
      if (typeof dcOffline.clearCorruptedConnections === 'function') {
        console.log('✅ clearCorruptedConnections method exists');
      } else {
        console.log('❌ clearCorruptedConnections method not found');
      }
      
    } else {
      console.log('❌ Driver connect offline service not available in global scope');
    }
    
  } catch (error) {
    console.error('❌ Driver connect offline test failed:', error);
  }
}

// Test 3: Check if PWA status hook has recovery function
async function testPWAStatusHookEnhancements() {
  console.log('\n3. Testing PWA Status Hook Enhancements...');
  
  try {
    if (typeof window !== 'undefined' && window.triggerSyncRecovery) {
      console.log('✅ triggerSyncRecovery function available globally');
      
      // Test the recovery function (this would actually trigger recovery)
      console.log('ℹ️ Recovery function available but not testing to avoid side effects');
      
    } else {
      console.log('❌ triggerSyncRecovery function not found in global scope');
    }
    
  } catch (error) {
    console.error('❌ PWA status hook test failed:', error);
  }
}

// Test 4: Check if debug PWA HTML has proper error handling
async function testDebugPWAEnhancements() {
  console.log('\n4. Testing Debug PWA Enhancements...');
  
  try {
    // Test if debug PWA page is accessible
    const response = await fetch('/debug-pwa.html');
    if (response.ok) {
      console.log('✅ Debug PWA page is accessible');
      
      const content = await response.text();
      
      // Check for enhanced error handling
      if (content.includes('initializeDebugPage')) {
        console.log('✅ Enhanced initialization found in debug PWA');
      } else {
        console.log('❌ Enhanced initialization not found in debug PWA');
      }
      
      if (content.includes('enhancedLog')) {
        console.log('✅ Enhanced logging found in debug PWA');
      } else {
        console.log('❌ Enhanced logging not found in debug PWA');
      }
      
    } else {
      console.log('❌ Debug PWA page not accessible');
    }
    
  } catch (error) {
    console.error('❌ Debug PWA test failed:', error);
  }
}

// Test 5: Check if sync recovery tool is available
async function testSyncRecoveryTool() {
  console.log('\n5. Testing Sync Recovery Tool...');
  
  try {
    const response = await fetch('/sync-recovery-tool.html');
    if (response.ok) {
      console.log('✅ Sync recovery tool is accessible');
      
      const content = await response.text();
      
      if (content.includes('checkSystemStatus')) {
        console.log('✅ System status check found in recovery tool');
      } else {
        console.log('❌ System status check not found in recovery tool');
      }
      
      if (content.includes('diagnoseSyncErrors')) {
        console.log('✅ Sync error diagnosis found in recovery tool');
      } else {
        console.log('❌ Sync error diagnosis not found in recovery tool');
      }
      
    } else {
      console.log('❌ Sync recovery tool not accessible');
    }
    
  } catch (error) {
    console.error('❌ Sync recovery tool test failed:', error);
  }
}

// Test 6: Simulate sync error scenario
async function testSyncErrorScenario() {
  console.log('\n6. Testing Sync Error Scenario...');
  
  try {
    // Check if we can access IndexedDB
    if ('indexedDB' in window) {
      console.log('✅ IndexedDB available');
      
      // Try to open the offline database
      const request = indexedDB.open('HaulingQROfflineDB', 1);
      
      await new Promise((resolve, reject) => {
        request.onsuccess = () => {
          console.log('✅ Offline database accessible');
          const db = request.result;
          
          // Check if connection queue store exists
          if (db.objectStoreNames.contains('connectionQueue')) {
            console.log('✅ Connection queue store exists');
          } else {
            console.log('ℹ️ Connection queue store not found (normal if no offline data)');
          }
          
          db.close();
          resolve();
        };
        
        request.onerror = () => {
          console.log('❌ Failed to open offline database');
          reject(request.error);
        };
        
        request.onupgradeneeded = () => {
          console.log('ℹ️ Database needs initialization (normal for first run)');
          resolve();
        };
      });
      
    } else {
      console.log('❌ IndexedDB not available');
    }
    
  } catch (error) {
    console.error('❌ Sync error scenario test failed:', error);
  }
}

// Test 7: Check network and PWA status
async function testNetworkAndPWAStatus() {
  console.log('\n7. Testing Network and PWA Status...');
  
  try {
    const status = {
      online: navigator.onLine,
      pwaMode: window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone === true,
      serviceWorkerSupported: 'serviceWorker' in navigator,
      serviceWorkerActive: false
    };
    
    // Check service worker
    if (status.serviceWorkerSupported) {
      try {
        const registration = await navigator.serviceWorker.getRegistration();
        status.serviceWorkerActive = !!registration?.active;
      } catch (error) {
        console.warn('Service worker check failed:', error);
      }
    }
    
    console.log('✅ Network Status:', status.online ? 'Online' : 'Offline');
    console.log('✅ PWA Mode:', status.pwaMode ? 'Active' : 'Browser Mode');
    console.log('✅ Service Worker:', status.serviceWorkerActive ? 'Active' : 'Inactive');
    
    if (!status.online) {
      console.log('⚠️ Currently offline - sync errors expected');
    }
    
    if (!status.serviceWorkerActive) {
      console.log('⚠️ Service worker not active - PWA features may not work');
    }
    
  } catch (error) {
    console.error('❌ Network and PWA status test failed:', error);
  }
}

// Run all tests
async function runAllTests() {
  console.log('Starting PWA Sync Error Fix Verification...\n');
  
  await testBackgroundSyncEnhancements();
  await testDriverConnectOfflineEnhancements();
  await testPWAStatusHookEnhancements();
  await testDebugPWAEnhancements();
  await testSyncRecoveryTool();
  await testSyncErrorScenario();
  await testNetworkAndPWAStatus();
  
  console.log('\n=== PWA Sync Error Fix Verification Complete ===');
  console.log('Check the results above to see which fixes are working properly.');
  console.log('If you see ❌ errors, those components may need additional fixes.');
}

// Auto-run tests
runAllTests().catch(error => {
  console.error('Test suite failed:', error);
});

// Export functions for manual testing
if (typeof window !== 'undefined') {
  window.testSyncErrorFixes = {
    runAllTests,
    testBackgroundSyncEnhancements,
    testDriverConnectOfflineEnhancements,
    testPWAStatusHookEnhancements,
    testDebugPWAEnhancements,
    testSyncRecoveryTool,
    testSyncErrorScenario,
    testNetworkAndPWAStatus
  };
  
  console.log('\n💡 Individual tests available as window.testSyncErrorFixes.*');
}