const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const auth = require('../middleware/auth');
const Joi = require('joi');
const { calculateTripsDurations, calculateDurationMetrics } = require('../utils/durationCalculator');

// Validation schemas
const tripSchema = Joi.object({
  assignment_id: Joi.number().integer().positive().required(),
  trip_number: Joi.number().integer().positive().required(),
  status: Joi.string().valid(
    'assigned', 'loading_start', 'loading_end',
    'unloading_start', 'unloading_end', 'trip_completed',
    'auto_assignment', 'dynamic_route', 'cancelled', 'stopped'
  ).optional(),
  loading_start_time: Joi.date().optional(),
  loading_end_time: Joi.date().optional(),
  unloading_start_time: Joi.date().optional(),
  unloading_end_time: Joi.date().optional(),
  trip_completed_time: Joi.date().optional(),
  actual_loading_location_id: Joi.number().integer().positive().optional(),
  actual_unloading_location_id: Joi.number().integer().positive().optional(),
  is_exception: Joi.boolean().optional(),
  exception_reason: Joi.string().max(1000).optional().allow(''),
  exception_approved_by: Joi.number().integer().positive().optional(),
  exception_approved_at: Joi.date().optional(),
  total_duration_minutes: Joi.number().integer().min(0).optional(),
  loading_duration_minutes: Joi.number().integer().min(0).optional(),
  travel_duration_minutes: Joi.number().integer().min(0).optional(),
  unloading_duration_minutes: Joi.number().integer().min(0).optional(),
  notes: Joi.string().max(1000).optional().allow('')
});

const updateTripSchema = tripSchema.fork(['assignment_id', 'trip_number'], (schema) => schema.optional());

// @route   GET /api/trips/filter-options
// @desc    Get available filter options for trips (statuses, drivers, routes)
// @access  Private
router.get('/filter-options', auth, async (req, res) => {
  try {
    // Get distinct statuses from actual trip data
    const statusQuery = `
      SELECT DISTINCT status, COUNT(*) as count
      FROM trip_logs
      WHERE status IS NOT NULL
      GROUP BY status
      ORDER BY count DESC
    `;

    // Get unique drivers from historical trip data
    const driversQuery = `
      SELECT DISTINCT
        COALESCE(t.performed_by_driver_name, d.full_name, sd.full_name) as driver_name
      FROM trip_logs t
      JOIN assignments a ON t.assignment_id = a.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      LEFT JOIN driver_shifts ds ON (
        ds.truck_id = a.truck_id
        AND ds.status = 'active'
        AND ds.start_date <= CURRENT_DATE
        AND (ds.end_date IS NULL OR ds.end_date >= CURRENT_DATE)
      )
      LEFT JOIN drivers sd ON ds.driver_id = sd.id
      WHERE COALESCE(t.performed_by_driver_name, d.full_name, sd.full_name) IS NOT NULL
      ORDER BY driver_name ASC
    `;

    // Get unique routes from trip data
    const routesQuery = `
      SELECT DISTINCT
        COALESCE(ll.name, al.name) as loading_location_name,
        COALESCE(ul.name, au.name) as unloading_location_name,
        COALESCE(ll.name, al.name) || ' → ' || COALESCE(ul.name, au.name) as route_name
      FROM trip_logs t
      JOIN assignments a ON t.assignment_id = a.id
      LEFT JOIN locations ll ON t.actual_loading_location_id = ll.id
      LEFT JOIN locations ul ON t.actual_unloading_location_id = ul.id
      LEFT JOIN locations al ON a.loading_location_id = al.id
      LEFT JOIN locations au ON a.unloading_location_id = au.id
      WHERE COALESCE(ll.name, al.name) IS NOT NULL
        AND COALESCE(ul.name, au.name) IS NOT NULL
      ORDER BY route_name ASC
    `;

    const [statusResult, driversResult, routesResult] = await Promise.all([
      query(statusQuery),
      query(driversQuery),
      query(routesQuery)
    ]);

    // Map statuses with descriptions
    const statusDescriptions = {
      'assigned': 'Trip assigned to driver',
      'loading_start': 'Loading in progress',
      'loading_end': 'Loading finished, traveling',
      'unloading_start': 'Unloading in progress',
      'unloading_end': 'Unloading finished, returning',
      'trip_completed': 'Fully completed trip',
      'auto_assignment': 'Automatically assigned by system',
      'dynamic_route': 'Progressive route discovery in progress',
      'cancelled': 'Cancelled or rejected',
      'stopped': 'Trip stopped (repair, maintenance, or finished)'
    };

    const statuses = statusResult.rows.map(row => ({
      value: row.status,
      label: row.status.split('_').map(word =>
        word.charAt(0).toUpperCase() + word.slice(1)
      ).join(' '),
      description: statusDescriptions[row.status] || row.status,
      count: parseInt(row.count)
    }));

    const drivers = driversResult.rows.map(row => ({
      value: row.driver_name,
      label: row.driver_name
    }));

    const routes = routesResult.rows.map(row => ({
      value: row.route_name,
      label: row.route_name,
      loading_location: row.loading_location_name,
      unloading_location: row.unloading_location_name
    }));

    res.json({
      success: true,
      data: {
        statuses,
        drivers,
        routes
      }
    });

  } catch (error) {
    console.error('Get filter options error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve filter options'
    });
  }
});


// @route   GET /api/trips
// @desc    Get all trips with filtering and search
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      status = '',
      assignment_id = '',
      date_from = '',
      date_to = '',
      is_exception = '',
      driver_name = '',
      route = '',
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    const validSortColumns = [
      'trip_number',
      'status',
      'created_at',
      'loading_start_time',
      'trip_completed_time',
      'performed_by_driver_name',  // For Assignment & Driver column
      'truck_number',              // For Assignment & Driver column
      'loading_location_name',     // For Route column
      'unloading_location_name',   // For Route column
      'total_duration_minutes'     // For Duration column
    ];
    const validSortOrders = ['asc', 'desc'];

    // Handle special sorting cases for joined columns
    let sortColumn = 'created_at';
    if (validSortColumns.includes(sortBy)) {
      switch (sortBy) {
        case 'performed_by_driver_name':
          sortColumn = 'COALESCE(t.performed_by_driver_name, d.full_name, sd.full_name)';
          break;
        case 'truck_number':
          sortColumn = 'tr.truck_number';
          break;
        case 'loading_location_name':
          sortColumn = 'COALESCE(ll.name, al.name)';
          break;
        case 'unloading_location_name':
          sortColumn = 'COALESCE(ul.name, au.name)';
          break;
        default:
          sortColumn = `t.${sortBy}`;
      }
    }
    const sortDirection = validSortOrders.includes(sortOrder.toLowerCase()) ? sortOrder.toUpperCase() : 'DESC';

    let whereConditions = [];
    let queryParams = [];
    let paramCount = 0;

    // Search functionality
    if (search) {
      paramCount++;
      whereConditions.push(`(
        t.trip_number::text ILIKE $${paramCount} OR
        tr.truck_number ILIKE $${paramCount} OR
        tr.license_plate ILIKE $${paramCount} OR
        d.full_name ILIKE $${paramCount} OR
        d.employee_id ILIKE $${paramCount} OR
        ll.name ILIKE $${paramCount} OR
        ul.name ILIKE $${paramCount} OR
        t.notes::text ILIKE $${paramCount} OR
        t.exception_reason ILIKE $${paramCount}
      )`);
      queryParams.push(`%${search}%`);
    }

    // Status filter
    if (status) {
      paramCount++;
      whereConditions.push(`t.status = $${paramCount}`);
      queryParams.push(status);
    }

    // Assignment filter
    if (assignment_id) {
      paramCount++;
      whereConditions.push(`t.assignment_id = $${paramCount}`);
      queryParams.push(parseInt(assignment_id));
    }

    // Date range filter with inclusive end date
    if (date_from) {
      paramCount++;
      whereConditions.push(`t.created_at >= $${paramCount}`);
      queryParams.push(date_from);
    }

    if (date_to) {
      paramCount++;
      // Make end date inclusive by adding 23:59:59.999 to cover the entire day
      whereConditions.push(`t.created_at <= $${paramCount}::date + INTERVAL '1 day' - INTERVAL '1 millisecond'`);
      queryParams.push(date_to);
    }

    // Exception filter
    if (is_exception !== '') {
      paramCount++;
      whereConditions.push(`t.is_exception = $${paramCount}`);
      queryParams.push(is_exception === 'true');
    }

    // Driver filter - search in historical performed_by_driver_name
    if (driver_name) {
      paramCount++;
      whereConditions.push(`COALESCE(t.performed_by_driver_name, d.full_name, sd.full_name) = $${paramCount}`);
      queryParams.push(driver_name);
    }

    // Route filter - match the route combination
    if (route) {
      paramCount++;
      whereConditions.push(`(COALESCE(ll.name, al.name) || ' → ' || COALESCE(ul.name, au.name)) = $${paramCount}`);
      queryParams.push(route);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Get total count - Enhanced with shift-based driver information
    const countQuery = `
      SELECT COUNT(*)
      FROM trip_logs t
      JOIN assignments a ON t.assignment_id = a.id
      JOIN dump_trucks tr ON a.truck_id = tr.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      LEFT JOIN locations ll ON COALESCE(t.actual_loading_location_id, a.loading_location_id) = ll.id
      LEFT JOIN locations ul ON COALESCE(t.actual_unloading_location_id, a.unloading_location_id) = ul.id
      LEFT JOIN locations al ON a.loading_location_id = al.id
      LEFT JOIN locations au ON a.unloading_location_id = au.id
      LEFT JOIN driver_shifts ds ON (
        ds.truck_id = a.truck_id
        AND ds.status = 'active'
        AND CURRENT_DATE BETWEEN ds.start_date AND ds.end_date
        AND (
          (ds.end_time < ds.start_time AND
            (CURRENT_TIME >= ds.start_time OR CURRENT_TIME <= ds.end_time))
          OR
          (ds.end_time >= ds.start_time AND
            CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
        )
      )
      LEFT JOIN drivers sd ON ds.driver_id = sd.id
      ${whereClause}
    `;
    const countResult = await query(countQuery, queryParams);
    const totalItems = parseInt(countResult.rows[0].count);

    // Get paginated data with joins - Enhanced with shift-based driver information
    const dataQuery = `
      SELECT
        t.id, t.trip_number, t.status, t.previous_status, t.loading_start_time, t.loading_end_time,
        t.unloading_start_time, t.unloading_end_time, t.trip_completed_time,
        t.is_exception, t.exception_reason, t.total_duration_minutes,
        t.loading_duration_minutes, t.travel_duration_minutes, t.unloading_duration_minutes,
        t.stopped_reported_at, t.stopped_reason, t.stopped_resolved_at, t.stopped_resolved_by,
        t.notes, t.created_at, t.updated_at,
        t.actual_loading_location_id, t.actual_unloading_location_id,
        t.location_sequence, t.is_extended_trip, t.workflow_type, t.baseline_trip_id, t.cycle_number,
        a.id as assignment_id, a.assigned_date, a.created_at as assignment_created_at, a.notes as assignment_notes,
        a.loading_location_id as assignment_loading_location_id, a.unloading_location_id as assignment_unloading_location_id,
        tr.id as truck_id, tr.truck_number, tr.license_plate,

        -- Assignment driver (may be NULL)
        d.id as driver_id, d.employee_id, d.full_name as driver_name,

        -- Historical driver information (who actually performed the trip)
        t.performed_by_driver_id,
        t.performed_by_driver_name,
        t.performed_by_employee_id,
        t.performed_by_shift_id,
        t.performed_by_shift_type,

        -- Current shift driver information
        ds.driver_id as current_shift_driver_id,
        sd.full_name as current_shift_driver_name,
        sd.employee_id as current_shift_employee_id,
        ds.shift_type as current_shift_type,
        ds.status as shift_status,

        -- Driver status for display
        CASE
          WHEN t.status IN ('trip_completed', 'cancelled', 'stopped') AND t.performed_by_driver_id IS NOT NULL THEN 'historical_driver'
          WHEN t.status IN ('trip_completed', 'cancelled', 'stopped') AND ds.driver_id IS NOT NULL THEN 'historical_fallback'
          WHEN ds.driver_id IS NOT NULL THEN 'active_shift'
          WHEN t.performed_by_driver_id IS NOT NULL THEN 'historical_driver'
          WHEN d.id IS NOT NULL THEN 'assigned_only'
          ELSE 'no_driver'
        END as driver_status,

        ll.id as loading_location_id,
        COALESCE(ll.name, 'Location ID: ' || COALESCE(t.actual_loading_location_id, a.loading_location_id)::text, 'No Loading Location') as loading_location_name,
        ul.id as unloading_location_id,
        COALESCE(ul.name, 'Location ID: ' || COALESCE(t.actual_unloading_location_id, a.unloading_location_id)::text, 'No Unloading Location') as unloading_location_name
      FROM trip_logs t
      JOIN assignments a ON t.assignment_id = a.id
      JOIN dump_trucks tr ON a.truck_id = tr.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      LEFT JOIN locations ll ON COALESCE(t.actual_loading_location_id, a.loading_location_id) = ll.id
      LEFT JOIN locations ul ON COALESCE(t.actual_unloading_location_id, a.unloading_location_id) = ul.id
      LEFT JOIN locations al ON a.loading_location_id = al.id
      LEFT JOIN locations au ON a.unloading_location_id = au.id
      LEFT JOIN driver_shifts ds ON (
        ds.truck_id = a.truck_id
        AND ds.status = 'active'
        AND ds.start_date <= CURRENT_DATE
        AND (ds.end_date IS NULL OR ds.end_date >= CURRENT_DATE)
        AND (
          ds.end_time IS NULL OR
          (ds.end_time < ds.start_time AND
            (CURRENT_TIME >= ds.start_time OR CURRENT_TIME <= ds.end_time))
          OR
          (ds.end_time >= ds.start_time AND
            CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
        )
      )
      LEFT JOIN drivers sd ON ds.driver_id = sd.id
      ${whereClause}
      ORDER BY ${sortColumn} ${sortDirection}
      LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;
    
    queryParams.push(parseInt(limit), offset);
    const tripsResult = await query(dataQuery, queryParams);

    // Calculate comprehensive durations for all trips
    const tripsWithDurations = calculateTripsDurations(tripsResult.rows);

    const totalPages = Math.ceil(totalItems / parseInt(limit));

    res.json({
      success: true,
      data: tripsWithDurations,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems,
        itemsPerPage: parseInt(limit),
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    });

  } catch (error) {
    console.error('Get trips error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve trips'
    });
  }
});

/**
 * @route   GET /api/trips/summary
 * @desc    Get completed trip count per truck (for TruckTripSummary)
 * @access  Private
 */
router.get('/summary', auth, async (req, res) => {
  try {
    // Enhanced Truck Trip Summary: Include all trip types including exceptions
    // Fixed: Remove date grouping to prevent duplicate entries per truck-location combination
    const summaryQuery = `
      SELECT
        dt.truck_number,
        dt.model,
        dt.license_plate,
        dt.capacity_tons,
        -- FIXED: Use actual locations only (where trucks actually went)
        ll.name AS loading_location_name,
        ul.name AS unloading_location_name,
        COUNT(tl.id) AS total_trips,
        COUNT(CASE WHEN tl.status = 'trip_completed' AND (tl.is_exception = false OR tl.is_exception IS NULL) THEN 1 END) AS completed_normal_trips,
        COUNT(CASE WHEN tl.status = 'exception_triggered' THEN 1 END) AS exception_triggered_trips,
        COUNT(CASE WHEN tl.status = 'trip_completed' AND tl.is_exception = true AND tl.exception_approved_at IS NOT NULL THEN 1 END) AS completed_exception_trips,
        COUNT(CASE WHEN tl.status = 'exception_pending' THEN 1 END) AS pending_exceptions,
        COUNT(CASE WHEN tl.status = 'cancelled' THEN 1 END) AS cancelled_trips,
        COUNT(CASE WHEN tl.status IN ('loading_start', 'loading_end', 'unloading_start', 'unloading_end') THEN 1 END) AS active_trips,
        MIN(tl.created_at) AS first_trip_time,
        MAX(tl.created_at) AS last_trip_time
      FROM
        dump_trucks dt
      JOIN
        assignments a ON dt.id = a.truck_id
      -- FIXED: Use actual locations only (where trucks actually went)
      LEFT JOIN
        locations ll ON tl.actual_loading_location_id = ll.id
      LEFT JOIN
        locations ul ON tl.actual_unloading_location_id = ul.id
      JOIN
        trip_logs tl ON a.id = tl.assignment_id
      WHERE
        tl.status IN ('trip_completed', 'exception_triggered', 'exception_pending', 'cancelled', 'loading_start', 'loading_end', 'unloading_start', 'unloading_end')
        -- FIXED: Only include trips with actual location data
        AND tl.actual_loading_location_id IS NOT NULL
        AND tl.actual_unloading_location_id IS NOT NULL
      GROUP BY
        dt.truck_number,
        dt.model,
        dt.license_plate,
        dt.capacity_tons,
        ll.name,
        ul.name
      ORDER BY
        dt.truck_number,
        total_trips DESC
    `;

    const result = await query(summaryQuery);

    res.json({
      success: true,
      data: result.rows
    });

  } catch (error) {
    console.error('Get trips summary error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve trip summary'
    });
  }
});

// @route   GET /api/trips/stats/durations
// @desc    Get comprehensive duration metrics for all trips
// @access  Private
router.get('/stats/durations', auth, async (req, res) => {
  try {
    const {
      date_from = '',
      date_to = '',
      status = '',
      assignment_id = ''
    } = req.query;

    let whereConditions = [];
    let queryParams = [];
    let paramCount = 0;

    // Date range filter with inclusive end date
    if (date_from) {
      paramCount++;
      whereConditions.push(`t.created_at >= $${paramCount}`);
      queryParams.push(date_from);
    }

    if (date_to) {
      paramCount++;
      // Make end date inclusive by adding 23:59:59.999 to cover the entire day
      whereConditions.push(`t.created_at <= $${paramCount}::date + INTERVAL '1 day' - INTERVAL '1 millisecond'`);
      queryParams.push(date_to);
    }

    // Status filter
    if (status) {
      paramCount++;
      whereConditions.push(`t.status = $${paramCount}`);
      queryParams.push(status);
    }

    // Assignment filter
    if (assignment_id) {
      paramCount++;
      whereConditions.push(`t.assignment_id = $${paramCount}`);
      queryParams.push(parseInt(assignment_id));
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Get all trips for duration analysis
    const dataQuery = `
      SELECT
        t.id, t.trip_number, t.status, t.loading_start_time, t.loading_end_time,
        t.unloading_start_time, t.unloading_end_time, t.trip_completed_time,
        t.total_duration_minutes, t.loading_duration_minutes,
        t.travel_duration_minutes, t.unloading_duration_minutes,
        t.created_at, t.updated_at,
        a.id as assignment_id,
        tr.truck_number
      FROM trip_logs t
      JOIN assignments a ON t.assignment_id = a.id
      JOIN dump_trucks tr ON a.truck_id = tr.id
      ${whereClause}
      ORDER BY t.created_at DESC
    `;

    const tripsResult = await query(dataQuery, queryParams);
    
    // Calculate comprehensive duration metrics
    const durationMetrics = calculateDurationMetrics(tripsResult.rows);

    res.json({
      success: true,
      data: durationMetrics
    });

  } catch (error) {
    console.error('Get duration stats error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve duration statistics'
    });
  }
});

// @route   GET /api/trips/stats/exceptions
// @desc    Get exception statistics for monitoring
// @access  Private
router.get('/stats/exceptions', auth, async (req, res) => {
  try {
    const statsQuery = `
      SELECT
        COUNT(*) as total_exceptions,
        COUNT(CASE WHEN a.status = 'pending' THEN 1 END) as pending_exceptions,
        COUNT(CASE WHEN a.status = 'approved' THEN 1 END) as approved_exceptions,
        COUNT(CASE WHEN a.status = 'rejected' THEN 1 END) as rejected_exceptions,
        COUNT(CASE WHEN a.exception_type = 'route_deviation' THEN 1 END) as route_deviations,
        COUNT(CASE WHEN a.exception_type = 'unassigned_trip' THEN 1 END) as unassigned_trips,
        COUNT(CASE WHEN a.severity = 'high' OR a.severity = 'critical' THEN 1 END) as high_priority_exceptions,
        AVG(EXTRACT(EPOCH FROM (a.reviewed_at - a.requested_at))/60) as avg_resolution_time_minutes
      FROM approvals a
      WHERE a.requested_at >= CURRENT_DATE - INTERVAL '30 days'
    `;

    const result = await query(statsQuery);
    const stats = result.rows[0];

    // Convert numeric strings to numbers and handle nulls
    const formattedStats = {
      total_exceptions: parseInt(stats.total_exceptions) || 0,
      pending_exceptions: parseInt(stats.pending_exceptions) || 0,
      approved_exceptions: parseInt(stats.approved_exceptions) || 0,
      rejected_exceptions: parseInt(stats.rejected_exceptions) || 0,
      route_deviations: parseInt(stats.route_deviations) || 0,
      unassigned_trips: parseInt(stats.unassigned_trips) || 0,
      high_priority_exceptions: parseInt(stats.high_priority_exceptions) || 0,
      avg_resolution_time_minutes: parseFloat(stats.avg_resolution_time_minutes) || 0
    };

    res.json({
      success: true,
      data: formattedStats
    });

  } catch (error) {
    console.error('Get exception stats error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve exception statistics'
    });
  }
});

// @route   GET /api/trips/:id
// @desc    Get single trip by ID
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await query(
      `SELECT
        t.*,
        a.assigned_date,
        tr.truck_number, tr.license_plate,

        -- Assignment driver (may be NULL)
        d.employee_id, d.full_name as driver_name,

        -- Historical driver information (who actually performed the trip)
        t.performed_by_driver_id,
        t.performed_by_driver_name,
        t.performed_by_employee_id,
        t.performed_by_shift_id,
        t.performed_by_shift_type,

        -- Current shift driver information
        ds.driver_id as current_shift_driver_id,
        sd.full_name as current_shift_driver_name,
        sd.employee_id as current_shift_employee_id,
        ds.shift_type as current_shift_type,

        COALESCE(ll.name, 'Location ID: ' || COALESCE(t.actual_loading_location_id, a.loading_location_id)::text, 'No Loading Location') as loading_location_name,
        COALESCE(ul.name, 'Location ID: ' || COALESCE(t.actual_unloading_location_id, a.unloading_location_id)::text, 'No Unloading Location') as unloading_location_name
       FROM trip_logs t
       JOIN assignments a ON t.assignment_id = a.id
       JOIN dump_trucks tr ON a.truck_id = tr.id
       LEFT JOIN drivers d ON a.driver_id = d.id
       LEFT JOIN locations ll ON COALESCE(t.actual_loading_location_id, a.loading_location_id) = ll.id
       LEFT JOIN locations ul ON COALESCE(t.actual_unloading_location_id, a.unloading_location_id) = ul.id
       LEFT JOIN driver_shifts ds ON (
         ds.truck_id = a.truck_id
         AND ds.status = 'active'
         AND CURRENT_DATE BETWEEN ds.start_date AND ds.end_date
         AND (
           (ds.end_time < ds.start_time AND
             (CURRENT_TIME >= ds.start_time OR CURRENT_TIME <= ds.end_time))
           OR
           (ds.end_time >= ds.start_time AND
             CURRENT_TIME BETWEEN ds.start_time AND ds.end_time)
         )
       )
       LEFT JOIN drivers sd ON ds.driver_id = sd.id
       WHERE t.id = $1`,
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Trip not found'
      });
    }

    res.json({
      success: true,
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Get trip error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to retrieve trip'
    });
  }
});

// @route   POST /api/trips
// @desc    Create new trip
// @access  Private
router.post('/', auth, async (req, res) => {
  try {
    // Validate input
    const { error } = tripSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.details[0].message
      });
    }

    const {
      assignment_id,
      trip_number,
      status = 'assigned',
      loading_start_time,
      loading_end_time,
      unloading_start_time,
      unloading_end_time,
      trip_completed_time,
      actual_loading_location_id,
      actual_unloading_location_id,
      is_exception = false,
      exception_reason = '',
      notes = ''
    } = req.body;

    // Check if assignment exists
    const assignmentCheck = await query(
      'SELECT id FROM assignments WHERE id = $1',
      [assignment_id]
    );

    if (assignmentCheck.rows.length === 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'Assignment not found'
      });
    }

    // Check for duplicate trip number for this assignment
    const duplicateCheck = await query(
      'SELECT id FROM trip_logs WHERE assignment_id = $1 AND trip_number = $2',
      [assignment_id, trip_number]
    );

    if (duplicateCheck.rows.length > 0) {
      return res.status(400).json({
        error: 'Duplicate Error',
        message: 'Trip number already exists for this assignment'
      });
    }

    // Insert new trip
    const result = await query(
      `INSERT INTO trip_logs 
       (assignment_id, trip_number, status, loading_start_time, loading_end_time,
        unloading_start_time, unloading_end_time, trip_completed_time,
        actual_loading_location_id, actual_unloading_location_id,
        is_exception, exception_reason, notes)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
       RETURNING *`,
      [assignment_id, trip_number, status, loading_start_time, loading_end_time,
       unloading_start_time, unloading_end_time, trip_completed_time,
       actual_loading_location_id, actual_unloading_location_id,
       is_exception, exception_reason, notes]
    );

    res.status(201).json({
      success: true,
      message: 'Trip created successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Create trip error:', error);
    
    if (error.code === '23505') { // Unique constraint violation
      return res.status(400).json({
        error: 'Duplicate Error',
        message: 'Trip number already exists for this assignment'
      });
    }

    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to create trip'
    });
  }
});

// @route   PUT /api/trips/:id
// @desc    Update trip
// @access  Private
router.put('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Validate input
    const { error } = updateTripSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation Error',
        message: error.details[0].message
      });
    }

    // Check if trip exists
    const existingTrip = await query(
      'SELECT * FROM trip_logs WHERE id = $1',
      [id]
    );

    if (existingTrip.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Trip not found'
      });
    }

    // Build update query dynamically
    const updates = [];
    const values = [];
    let paramCount = 0;

    const allowedFields = [
      'status', 'loading_start_time', 'loading_end_time',
      'unloading_start_time', 'unloading_end_time', 'trip_completed_time',
      'actual_loading_location_id', 'actual_unloading_location_id',
      'is_exception', 'exception_reason', 'exception_approved_by',
      'exception_approved_at', 'total_duration_minutes',
      'loading_duration_minutes', 'travel_duration_minutes',
      'unloading_duration_minutes', 'notes'
    ];

    allowedFields.forEach(field => {
      if (req.body[field] !== undefined) {
        paramCount++;
        updates.push(`${field} = $${paramCount}`);
        values.push(req.body[field]);
      }
    });

    if (updates.length === 0) {
      return res.status(400).json({
        error: 'Validation Error',
        message: 'No fields to update'
      });
    }

    // Add updated_at
    paramCount++;
    updates.push(`updated_at = $${paramCount}`);
    values.push(new Date());

    // Add id for WHERE clause
    paramCount++;
    values.push(id);

    const updateQuery = `
      UPDATE trip_logs 
      SET ${updates.join(', ')}
      WHERE id = $${paramCount}
      RETURNING *
    `;

    const result = await query(updateQuery, values);

    res.json({
      success: true,
      message: 'Trip updated successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('Update trip error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to update trip'
    });
  }
});

// @route   DELETE /api/trips/:id
// @desc    Delete trip
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if trip exists
    const existingTrip = await query(
      'SELECT * FROM trip_logs WHERE id = $1',
      [id]
    );

    if (existingTrip.rows.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Trip not found'
      });
    }

    // Delete trip
    await query('DELETE FROM trip_logs WHERE id = $1', [id]);

    res.json({
      success: true,
      message: 'Trip deleted successfully'
    });

  } catch (error) {
    console.error('Delete trip error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to delete trip'
    });
  }
});

// @route   POST /api/trips/:id/stop
// @desc    Stop trip with two options: pause or terminate
// @access  Private
router.post('/:id/stop', auth, async (req, res) => {
  try {
    const tripId = parseInt(req.params.id);
    const { reason, stop_type = 'pause' } = req.body; // 'pause' or 'terminate'

    if (!tripId || isNaN(tripId)) {
      return res.status(400).json({
        success: false,
        message: 'Valid trip ID is required'
      });
    }

    if (!reason || reason.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Stop reason is required'
      });
    }

    if (!['pause', 'terminate'].includes(stop_type)) {
      return res.status(400).json({
        success: false,
        message: 'Stop type must be either "pause" or "terminate"'
      });
    }

    // Check if trip exists and is not already completed
    const tripCheck = await query(
      'SELECT id, status, assignment_id FROM trip_logs WHERE id = $1',
      [tripId]
    );

    if (tripCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Trip not found'
      });
    }

    const trip = tripCheck.rows[0];

    if (trip.status === 'trip_completed') {
      return res.status(400).json({
        success: false,
        message: 'Cannot stop a completed trip'
      });
    }

    if (trip.status === 'stopped') {
      return res.status(400).json({
        success: false,
        message: 'Trip is already stopped'
      });
    }

    // Handle stop based on type
    let result;
    if (stop_type === 'pause') {
      // PAUSE: Save current status and set to stopped (can be resumed)
      result = await query(`
        UPDATE trip_logs
        SET
          previous_status = status,
          status = 'stopped',
          stopped_reported_at = CURRENT_TIMESTAMP,
          stopped_reason = $1,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
        RETURNING *
      `, [reason.trim(), tripId]);
    } else {
      // TERMINATE: Mark as stopped (next scan will start new trip)
      result = await query(`
        UPDATE trip_logs
        SET
          status = 'stopped',
          stopped_reported_at = CURRENT_TIMESTAMP,
          stopped_reason = $1,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
        RETURNING *
      `, [reason.trim(), tripId]);
    }

    if (result.rows.length === 0) {
      return res.status(500).json({
        success: false,
        message: 'Failed to update trip status'
      });
    }

    const message = stop_type === 'pause'
      ? 'Trip paused - can be resumed later'
      : 'Trip stopped - next scan will start new trip';

    res.json({
      success: true,
      message: message,
      stop_type: stop_type,
      trip: result.rows[0]
    });

  } catch (error) {
    console.error('Error marking trip as stopped:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/trips/:id/resolve-stopped
// @desc    Resolve stopped trip and restore trip to previous status
// @access  Private
router.post('/:id/resolve-stopped', auth, async (req, res) => {
  try {
    const tripId = parseInt(req.params.id);
    const { resolution_notes } = req.body;

    if (!tripId || isNaN(tripId)) {
      return res.status(400).json({
        success: false,
        message: 'Valid trip ID is required'
      });
    }

    // Check if trip exists and is in stopped status
    const tripCheck = await query(
      'SELECT id, status, assignment_id FROM trip_logs WHERE id = $1',
      [tripId]
    );

    if (tripCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Trip not found'
      });
    }

    const trip = tripCheck.rows[0];

    if (trip.status !== 'stopped') {
      return res.status(400).json({
        success: false,
        message: 'Trip is not in stopped status'
      });
    }

    // Get the trip details to determine restoration approach
    const tripDetails = await query(`
      SELECT
        previous_status, status,
        loading_start_time, loading_end_time,
        unloading_start_time, unloading_end_time
      FROM trip_logs
      WHERE id = $1
    `, [tripId]);

    if (tripDetails.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Trip details not found'
      });
    }

    const details = tripDetails.rows[0];
    let restoreStatus;
    let resolutionMessage;

    if (details.previous_status) {
      // This was a PAUSED stop - restore to previous status
      restoreStatus = details.previous_status;
      resolutionMessage = `Stop resolved. Trip resumed at ${restoreStatus} phase.`;
    } else {
      // This was a TERMINATED stop - cannot be resumed (trip already completed)
      return res.status(400).json({
        success: false,
        message: 'Cannot resolve terminated stop. Trip was already completed.'
      });
    }

    // Update trip status to resolved (only for paused stops)
    const result = await query(`
      UPDATE trip_logs
      SET
        status = $1,
        previous_status = NULL,
        stopped_resolved_at = CURRENT_TIMESTAMP,
        stopped_resolved_by = $2,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
      RETURNING *
    `, [restoreStatus, req.user.id, tripId]);

    if (result.rows.length === 0) {
      return res.status(500).json({
        success: false,
        message: 'Failed to resolve stopped trip'
      });
    }

    res.json({
      success: true,
      message: resolutionMessage,
      trip: result.rows[0]
    });

  } catch (error) {
    console.error('Error resolving stopped trip:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   GET /api/trips/trip-numbers/statistics
// @desc    Get trip number statistics and check for duplicates
// @access  Private
router.get('/trip-numbers/statistics', auth, async (req, res) => {
  try {
    const stats = await query(`
      SELECT
        COUNT(*) as total_trips,
        COUNT(DISTINCT trip_number) as unique_trip_numbers,
        COUNT(*) - COUNT(DISTINCT trip_number) as duplicates,
        MIN(trip_number) as min_trip_number,
        MAX(trip_number) as max_trip_number
      FROM trip_logs
      WHERE trip_number > 0
    `);

    const result = stats.rows[0];
    const data = {
      totalTrips: parseInt(result.total_trips),
      uniqueTripNumbers: parseInt(result.unique_trip_numbers),
      duplicates: parseInt(result.duplicates),
      minTripNumber: parseInt(result.min_trip_number) || 0,
      maxTripNumber: parseInt(result.max_trip_number) || 0,
      hasNoDuplicates: parseInt(result.duplicates) === 0
    };

    res.json({
      success: true,
      data: data,
      message: data.hasNoDuplicates ?
        'All trip numbers are unique' :
        `Found ${data.duplicates} duplicate trip numbers`
    });

  } catch (error) {
    console.error('Get trip number statistics error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to get trip number statistics'
    });
  }
});

// @route   POST /api/trips/trip-numbers/fix-duplicates
// @desc    Fix duplicate trip numbers by making them globally unique
// @access  Private
router.post('/trip-numbers/fix-duplicates', auth, async (req, res) => {
  try {
    // Simple fix: update each trip to use its ID as trip number
    const updateResult = await query(`
      UPDATE trip_logs
      SET trip_number = id
      WHERE trip_number != id
    `);

    const result = {
      success: true,
      message: `Fixed ${updateResult.rowCount} trips with duplicate numbers`,
      updatedTrips: updateResult.rowCount
    };

    res.json({
      success: true,
      data: result,
      message: result.message
    });

  } catch (error) {
    console.error('Fix trip number duplicates error:', error);
    res.status(500).json({
      error: 'Server Error',
      message: 'Failed to fix trip number duplicates'
    });
  }
});

module.exports = router;