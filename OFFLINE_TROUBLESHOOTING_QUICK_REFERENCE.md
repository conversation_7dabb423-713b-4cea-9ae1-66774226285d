# Offline Mode Troubleshooting Quick Reference

## 🚨 Emergency Fixes

### Infinite Refresh Loop
```bash
# 1. Clear browser cache completely
# Chrome: Dev Tools > Application > Storage > Clear storage

# 2. Check for object dependencies in useEffect
# Look for: useEffect(() => {}, [objectDependency])
# Fix: Use primitive values or useMemo

# 3. Restart development server
npm run dev
```

### Service Worker Not Working
```bash
# 1. Force service worker update
# Browser: Dev Tools > Application > Service Workers > Update

# 2. Clear all caches
# Browser: Dev Tools > Application > Storage > Clear storage

# 3. Check service worker registration
# Console: navigator.serviceWorker.getRegistrations()
```

### Offline Data Not Syncing
```bash
# 1. Check IndexedDB data
# Browser: Dev Tools > Application > IndexedDB

# 2. Verify network status
# Console: navigator.onLine

# 3. Trigger manual sync
# Click sync button in app or run: triggerSync()
```

## 🔍 Quick Diagnostics

### Check Offline Status
```javascript
// In browser console
console.log('Online:', navigator.onLine);
console.log('Service Worker:', 'serviceWorker' in navigator);
console.log('Background Sync:', 'sync' in window.ServiceWorkerRegistration.prototype);
```

### Verify Service Worker
```javascript
// Check service worker status
navigator.serviceWorker.getRegistrations().then(registrations => {
  console.log('SW Registrations:', registrations.length);
  registrations.forEach(reg => console.log('SW State:', reg.active?.state));
});
```

### Check Offline Data
```javascript
// Open IndexedDB and check data
const request = indexedDB.open('hauling-qr-offline');
request.onsuccess = (event) => {
  const db = event.target.result;
  console.log('DB Object Stores:', Array.from(db.objectStoreNames));
};
```

## ⚡ Common Issues & Solutions

| Issue | Symptom | Quick Fix |
|-------|---------|-----------|
| **Refresh Loop** | Page keeps reloading | Check useEffect dependencies |
| **SW Not Updating** | Old cache persists | Clear browser cache, update SW |
| **No Offline Mode** | App doesn't work offline | Check SW registration and PWA routes |
| **Sync Failing** | Data not syncing | Check network status and IndexedDB |
| **Auth Redirects** | Login screen appears offline | Verify offline detection in components |

## 🛠️ Development Commands

```bash
# Start development server
npm run dev

# Test service worker
node test-service-worker-functionality.js

# Run final validation
node final-offline-validation.js

# Check database connection
npm run db:migrate
```

## 📱 Browser Testing

### Chrome DevTools
1. **Application Tab** > Service Workers
2. **Application Tab** > IndexedDB
3. **Network Tab** > Throttling > Offline
4. **Console** > Check for errors

### Firefox DevTools
1. **Storage Tab** > Service Workers
2. **Storage Tab** > IndexedDB
3. **Network Tab** > Disable cache
4. **Console** > Monitor messages

### Safari DevTools
1. **Storage Tab** > Service Workers
2. **Storage Tab** > IndexedDB
3. **Network Tab** > Disable caches
4. **Console** > Check logs

## 🔧 Code Patterns

### ✅ Correct Patterns
```javascript
// Stable useEffect
useEffect(() => {
  // Logic
}, [primitiveValue]);

// Stable event handler
const handleClick = useCallback(() => {
  // Logic
}, [dependency]);

// Stable computed value
const value = useMemo(() => computation(), [input]);
```

### ❌ Patterns to Avoid
```javascript
// Causes infinite loops
useEffect(() => {
  setState({...state, new: 'value'});
}, [state]);

// Creates new function every render
const handleClick = () => {
  // Logic without useCallback
};

// Missing dependency array
useEffect(() => {
  // Logic
}); // No dependency array
```

## 📞 Support Contacts

- **Development Issues**: Check console logs and component patterns
- **Service Worker Issues**: Clear cache and check registration
- **Database Issues**: Verify IndexedDB and sync status
- **Network Issues**: Test with browser offline mode

## 🎯 Quick Health Check

Run this in browser console:
```javascript
// Complete system check
const healthCheck = async () => {
  console.log('🔍 System Health Check');
  console.log('Online:', navigator.onLine);
  console.log('SW Support:', 'serviceWorker' in navigator);
  console.log('IndexedDB Support:', 'indexedDB' in window);
  
  if ('serviceWorker' in navigator) {
    const registrations = await navigator.serviceWorker.getRegistrations();
    console.log('SW Registered:', registrations.length > 0);
  }
  
  console.log('✅ Health check complete');
};

healthCheck();
```