import { useState, useEffect, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { backgroundSync } from '../services/backgroundSync';
// Removed tripScannerOffline import - TripScanner now operates online-only
import { driverConnectOffline } from '../services/driverConnectOffline';

/**
 * Custom hook for PWA status monitoring and sync management
 * Provides real-time status updates for offline functionality
 */
export const usePWAStatus = () => {
  const location = useLocation();
  
  // Network and sync states
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [syncStatus, setSyncStatus] = useState('synced'); // 'synced', 'pending', 'syncing', 'error'
  // Removed queuedScans - TripScanner now operates online-only
  const [queuedConnections, setQueuedConnections] = useState(0);
  
  // PWA detection
  const [isPWA, setIsPWA] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState(null);
  const [syncError, setSyncError] = useState(null);

  // PWA installation state
  const [installPrompt, setInstallPrompt] = useState(null);
  const [isInstalled, setIsInstalled] = useState(false);

  // Enhanced PWA mode detection and service worker communication with comprehensive error handling
  const detectAndSendPWAMode = useCallback(() => {
    try {
      // Enhanced PWA mode detection with multiple methods
      const detectionMethods = [];
      let isPWAMode = false;
      
      try {
        // Method 1: Display mode media query
        const standaloneMatch = window.matchMedia('(display-mode: standalone)').matches;
        detectionMethods.push({ method: 'display-mode media query', result: standaloneMatch });
        
        // Method 2: iOS standalone
        const iOSStandalone = window.navigator.standalone === true;
        detectionMethods.push({ method: 'iOS navigator.standalone', result: iOSStandalone });
        
        // Method 3: Android app referrer
        const androidApp = document.referrer.includes('android-app://');
        detectionMethods.push({ method: 'Android app referrer', result: androidApp });
        
        // Method 4: URL parameters (some PWA launchers add these)
        const urlParams = new URLSearchParams(window.location.search);
        const pwaParam = urlParams.has('pwa') || urlParams.has('standalone');
        detectionMethods.push({ method: 'URL parameters', result: pwaParam });
        
        // Method 5: Window features (some PWA contexts have limited window features)
        const limitedFeatures = !window.locationbar?.visible && !window.menubar?.visible;
        detectionMethods.push({ method: 'limited window features', result: limitedFeatures });
        
        isPWAMode = standaloneMatch || iOSStandalone || androidApp || pwaParam;
        
        console.log('[PWAStatus] PWA mode detection completed:', {
          isPWAMode,
          detectionMethods,
          userAgent: navigator.userAgent.substring(0, 100) + '...'
        });
        
      } catch (detectionError) {
        console.error('[PWAStatus] Error during PWA mode detection:', detectionError);
        // Fallback to basic detection
        isPWAMode = window.matchMedia('(display-mode: standalone)').matches;
        detectionMethods.push({ method: 'fallback basic detection', result: isPWAMode, error: detectionError.message });
      }
      
      setIsPWA(isPWAMode);
      
      // Send PWA mode status to service worker with enhanced data and error handling
      try {
        if ('serviceWorker' in navigator) {
          if (navigator.serviceWorker.controller) {
            const message = {
              type: 'PWA_MODE_STATUS',
              isPWA: isPWAMode,
              currentPath: window.location.pathname,
              timestamp: new Date().toISOString(),
              userAgent: navigator.userAgent,
              displayMode: window.matchMedia('(display-mode: standalone)').matches ? 'standalone' : 'browser',
              source: 'pwa-status-hook',
              detectionMethods,
              requestId: Math.random().toString(36).substr(2, 9)
            };
            
            navigator.serviceWorker.controller.postMessage(message);
            console.log('[PWAStatus] Enhanced PWA mode status sent to service worker:', {
              isPWA: isPWAMode,
              path: message.currentPath,
              displayMode: message.displayMode,
              methodsUsed: detectionMethods.length,
              requestId: message.requestId
            });
            
          } else {
            console.warn('[PWAStatus] Service worker controller not available for PWA mode communication');
            console.warn('[PWAStatus] Service worker state:', {
              supported: 'serviceWorker' in navigator,
              controller: !!navigator.serviceWorker.controller,
              ready: navigator.serviceWorker.ready ? 'pending' : 'not available'
            });
          }
        } else {
          console.warn('[PWAStatus] Service worker not supported in this browser');
        }
      } catch (communicationError) {
        console.error('[PWAStatus] Error communicating with service worker:', communicationError);
        console.error('[PWAStatus] Communication context:', {
          isPWAMode,
          currentPath: window.location.pathname,
          serviceWorkerSupported: 'serviceWorker' in navigator,
          controllerAvailable: !!(navigator.serviceWorker?.controller)
        });
      }
      
      return isPWAMode;
      
    } catch (error) {
      console.error('[PWAStatus] Critical error in detectAndSendPWAMode:', error);
      console.error('[PWAStatus] Error context:', {
        location: window.location.href,
        userAgent: navigator.userAgent.substring(0, 100) + '...',
        timestamp: new Date().toISOString()
      });
      
      // Fallback to basic detection in case of critical error
      try {
        const fallbackMode = window.matchMedia('(display-mode: standalone)').matches;
        setIsPWA(fallbackMode);
        return fallbackMode;
      } catch (fallbackError) {
        console.error('[PWAStatus] Even fallback detection failed:', fallbackError);
        setIsPWA(false);
        return false;
      }
    }
  }, []);

  // Detect PWA mode and communicate with service worker
  useEffect(() => {
    // Initial detection
    detectAndSendPWAMode();
    
    // Listen for display mode changes with enhanced detection
    const mediaQuery = window.matchMedia('(display-mode: standalone)');
    const handleDisplayModeChange = (e) => {
      console.log('[PWAStatus] Display mode changed:', e.matches ? 'standalone' : 'browser');
      detectAndSendPWAMode();
    };
    
    mediaQuery.addListener(handleDisplayModeChange);
    
    // Also listen for visibility changes to re-detect PWA mode
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log('[PWAStatus] Page became visible, re-detecting PWA mode');
        detectAndSendPWAMode();
      }
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      mediaQuery.removeListener(handleDisplayModeChange);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [detectAndSendPWAMode]);

  // Send PWA mode updates when location changes with enhanced communication
  useEffect(() => {
    console.log('[PWAStatus] Location changed to:', location.pathname);
    
    // Re-detect and send PWA mode status on location changes
    const currentPWAMode = detectAndSendPWAMode();
    
    // Send additional location-specific information to service worker
    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({
        type: 'LOCATION_CHANGE',
        currentPath: location.pathname,
        isPWA: currentPWAMode,
        timestamp: new Date().toISOString(),
        source: 'pwa-status-hook'
      });
      console.log('[PWAStatus] Location change notification sent to service worker:', location.pathname);
    }
  }, [location.pathname, detectAndSendPWAMode]);



  // Enhanced sync error recovery function
  const recoverFromSyncError = useCallback(async () => {
    console.log('[PWA] Starting sync error recovery...');
    
    try {
      setSyncStatus('syncing');
      setSyncError(null);
      
      // Step 1: Try to recover failed connections
      const recoveryResults = await backgroundSync.recoverFailedConnections();
      console.log('[PWA] Recovery results:', recoveryResults);
      
      // Step 2: Clear any corrupted data
      const clearResults = await backgroundSync.clearCorruptedData();
      console.log('[PWA] Clear corrupted data results:', clearResults);
      
      // Step 3: Attempt sync again
      const syncResults = await backgroundSync.startSync();
      console.log('[PWA] Post-recovery sync results:', syncResults);
      
      if (syncResults && syncResults.driverConnections) {
        setSyncStatus('synced');
        setLastSyncTime(new Date().toISOString());
        
        // Update queue counts after successful recovery
        try {
          const connectionCount = await driverConnectOffline.getPendingCount();
          setQueuedConnections(connectionCount);
        } catch (countError) {
          console.error('Failed to update queue counts after recovery:', countError);
        }
        
        return {
          success: true,
          message: `Recovery successful. Synced ${syncResults.driverConnections?.synced || 0} items.`,
          recoveryResults,
          syncResults
        };
      } else {
        setSyncStatus('error');
        setSyncError('Recovery failed - sync still not working');
        
        return {
          success: false,
          message: 'Recovery failed - sync still not working',
          recoveryResults,
          syncResults
        };
      }
    } catch (error) {
      console.error('[PWA] Sync error recovery failed:', error);
      setSyncStatus('error');
      setSyncError(`Recovery failed: ${error.message}`);
      
      return {
        success: false,
        message: `Recovery failed: ${error.message}`,
        error
      };
    }
  }, []);

  // Trigger manual sync with enhanced error handling
  const triggerSync = useCallback(async () => {
    if (!navigator.onLine) {
      console.log('Cannot sync while offline');
      return { success: false, message: 'Cannot sync while offline' };
    }

    try {
      setSyncStatus('syncing');
      setSyncError(null);

      const results = await backgroundSync.startSync();

      if (results && results.driverConnections) {
        setSyncStatus('synced');
        setLastSyncTime(new Date().toISOString());

        // Update queue counts directly after sync
        try {
          // Only check driver connections - TripScanner operates online-only
          const connectionCount = await driverConnectOffline.getPendingCount();
          setQueuedConnections(connectionCount);
        } catch (countError) {
          console.error('Failed to update queue counts after sync:', countError);
        }

        const totalSynced = (results.driverConnections?.synced || 0); // Only driver connections
        return {
          success: true,
          message: `Synced ${totalSynced} items successfully`,
          results
        };
      } else {
        // If sync failed, try recovery
        console.log('[PWA] Sync failed, attempting recovery...');
        return await recoverFromSyncError();
      }
    } catch (error) {
      console.error('Manual sync failed:', error);
      
      // If sync threw an error, try recovery
      console.log('[PWA] Sync threw error, attempting recovery...');
      return await recoverFromSyncError();
    }
  }, [recoverFromSyncError]); // Add recoverFromSyncError dependency

  // Handle PWA installation
  const installPWA = useCallback(async () => {
    if (!installPrompt) {
      return { success: false, message: 'Installation not available' };
    }

    try {
      await installPrompt.prompt();
      const choiceResult = await installPrompt.userChoice;

      if (choiceResult.outcome === 'accepted') {
        setIsInstalled(true);
        setInstallPrompt(null);
        return { success: true, message: 'PWA installed successfully' };
      } else {
        return { success: false, message: 'Installation cancelled by user' };
      }
    } catch (error) {
      console.error('PWA installation failed:', error);
      return { success: false, message: error.message };
    }
  }, [installPrompt]);

  // Network status monitoring
  useEffect(() => {
    const handleOnline = async () => {
      setIsOnline(true);
      setSyncError(null);

      // Auto-trigger sync when coming back online
      // Check queue counts and trigger sync directly to avoid circular dependencies
      try {
        const connectionCount = await driverConnectOffline.getPendingCount();

        if (connectionCount > 0) {
          // Trigger sync directly without using the triggerSync function to avoid circular dependencies
          setTimeout(async () => {
            try {
              setSyncStatus('syncing');
              const results = await backgroundSync.startSync();

              if (results && results.driverConnections) {
                setSyncStatus('synced');
                setLastSyncTime(new Date().toISOString());

                // Update queue counts directly after auto-sync
                try {
                  const connectionCount = await driverConnectOffline.getPendingCount();
                  setQueuedConnections(connectionCount);
                } catch (countError) {
                  console.error('Failed to update queue counts after auto-sync:', countError);
                }
              } else {
                setSyncStatus('error');
                setSyncError('Auto-sync failed - no valid results');
              }
            } catch (error) {
              console.error('Auto-sync failed:', error);
              setSyncStatus('error');
              setSyncError(error.message);
            }
          }, 1000); // Small delay to ensure connection is stable
        }
      } catch (error) {
        console.error('Failed to check queue counts on reconnect:', error);
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
      setSyncStatus('pending');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []); // Remove updateQueueCounts dependency to prevent infinite loop

  // PWA installation prompt handling
  useEffect(() => {
    const handleBeforeInstallPrompt = (e) => {
      e.preventDefault();
      setInstallPrompt(e);
    };

    const handleAppInstalled = () => {
      setIsInstalled(true);
      setInstallPrompt(null);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    // Check if already installed
    if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
      setIsInstalled(true);
    }

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  // Periodic queue count updates - check regularly to detect new offline data
  useEffect(() => {
    const updateCounts = async () => {
      try {
        // Only check driver connections - TripScanner operates online-only
        const connectionCount = await driverConnectOffline.getPendingCount();
        
        // Add debug logging to help troubleshoot sync button issues
        if (connectionCount > 0) {
          console.log(`[PWAStatus] Found ${connectionCount} queued connections, isOnline: ${isOnline}, isPWA: ${isPWA}`);
        }

        setQueuedConnections(connectionCount);

        // Update overall sync status based on queue counts and online status
        const totalQueued = connectionCount;
        setSyncStatus(prevStatus => {
          const newStatus = (() => {
            // If we have queued items and we're not already syncing
            if (totalQueued > 0 && prevStatus !== 'syncing') {
              return isOnline ? 'pending' : 'pending';
            } 
            // If no queued items and not syncing, we're synced
            else if (totalQueued === 0 && prevStatus !== 'syncing') {
              return 'synced';
            }
            // Keep current status if syncing
            return prevStatus;
          })();
          
          if (newStatus !== prevStatus) {
            console.log(`[PWAStatus] Sync status changed from ${prevStatus} to ${newStatus}, queued: ${totalQueued}`);
          }
          
          return newStatus;
        });
      } catch (error) {
        console.error('Failed to update queue counts:', error);
      }
    };

    // Initial update
    updateCounts();

    // Set up interval for periodic updates
    const interval = setInterval(updateCounts, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, [isOnline]); // Remove syncStatus dependency to prevent infinite loop

  // Enhanced service worker communication and PWA mode handling
  useEffect(() => {
    if ('serviceWorker' in navigator) {
      const handleServiceWorkerMessage = (event) => {
        console.log('[PWAStatus] Received message from service worker:', event.data);
        
        if (event.data && event.data.type === 'SYNC_STATUS_UPDATE') {
          const { status, queuedConnections: connections } = event.data;

          setSyncStatus(status);
          // Removed queuedScans - TripScanner operates online-only
          setQueuedConnections(connections || 0);

          if (status === 'synced') {
            setLastSyncTime(new Date().toISOString());
            setSyncError(null);
          } else if (status === 'error') {
            setSyncError(event.data.error || 'Sync failed');
          }
        } else if (event.data && event.data.type === 'TRIGGER_SYNC') {
          // Service worker is requesting a sync - trigger it immediately
          console.log('[PWAStatus] Service worker requested sync:', event.data.syncType);
          triggerSync();
        } else if (event.data && event.data.type === 'REQUEST_PWA_MODE') {
          // Enhanced response to service worker PWA mode requests
          console.log('[PWAStatus] Service worker requested PWA mode status');
          
          const currentPWAMode = detectAndSendPWAMode();
          console.log('[PWAStatus] Responded to service worker PWA mode request with enhanced detection:', currentPWAMode);
        } else if (event.data && event.data.type === 'PWA_MODE_CONFIRMATION') {
          // Service worker confirming receipt of PWA mode status
          console.log('[PWAStatus] Service worker confirmed PWA mode status:', event.data.confirmedMode);
        } else if (event.data && event.data.type === 'SERVICE_WORKER_READY') {
          // Service worker is ready - send current PWA mode status
          console.log('[PWAStatus] Service worker ready, sending current PWA mode status');
          detectAndSendPWAMode();
        }
      };

      // Enhanced service worker registration and communication setup
      const setupServiceWorkerCommunication = async () => {
        try {
          // Wait for service worker to be ready
          await navigator.serviceWorker.ready;
          
          // Send initial PWA mode status when service worker is ready
          console.log('[PWAStatus] Service worker ready, sending initial PWA mode status');
          detectAndSendPWAMode();
          
          // Set up message listener
          navigator.serviceWorker.addEventListener('message', handleServiceWorkerMessage);
          
          // Send ready notification to service worker
          if (navigator.serviceWorker.controller) {
            navigator.serviceWorker.controller.postMessage({
              type: 'CLIENT_READY',
              timestamp: new Date().toISOString(),
              source: 'pwa-status-hook'
            });
          }
        } catch (error) {
          console.error('[PWAStatus] Failed to setup service worker communication:', error);
        }
      };

      setupServiceWorkerCommunication();

      // Cleanup listener on unmount
      return () => {
        navigator.serviceWorker.removeEventListener('message', handleServiceWorkerMessage);
      };
    }
  }, [triggerSync, detectAndSendPWAMode]);

  // Export recovery function to global scope for debug tools
  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.triggerSyncRecovery = recoverFromSyncError;
    }
    
    return () => {
      if (typeof window !== 'undefined') {
        delete window.triggerSyncRecovery;
      }
    };
  }, [recoverFromSyncError]);

  return {
    // Network status
    isOnline,
    
    // Sync status
    syncStatus,
    // queuedScans, // Removed - TripScanner operates online-only
    queuedConnections,
    lastSyncTime,
    syncError,
    
    // PWA status
    isPWA,
    installPrompt,
    isInstalled,
    
    // Actions
    triggerSync,
    installPWA,
    recoverFromSyncError, // Export recovery function
    
    // Computed values
    totalQueued: queuedConnections, // Only driver connections
    canSync: isOnline && queuedConnections > 0, // Allow sync in both PWA and browser modes
    canInstall: !!installPrompt && !isInstalled
  };
};

export default usePWAStatus;
