// Clear driver-connect PWA cache specifically
// Run this in browser console on the driver-connect page

async function clearDriverConnectCache() {
  console.log('🔧 Clearing driver-connect PWA cache...');
  
  try {
    // 1. Unregister ALL service workers
    const registrations = await navigator.serviceWorker.getRegistrations();
    console.log(`Found ${registrations.length} service worker registrations`);
    
    for (const registration of registrations) {
      await registration.unregister();
      console.log('✅ Unregistered SW:', registration.scope);
    }
    
    // 2. Clear ALL caches (especially hot-update files)
    const cacheNames = await caches.keys();
    console.log(`Found ${cacheNames.length} caches:`, cacheNames);
    
    for (const cacheName of cacheNames) {
      const cache = await caches.open(cacheName);
      const requests = await cache.keys();
      
      // Log what we're deleting
      for (const request of requests) {
        if (request.url.includes('hot-update') || request.url.includes('main.')) {
          console.log('🗑️ Deleting hot-update file:', request.url);
        }
      }
      
      await caches.delete(cacheName);
      console.log('✅ Deleted cache:', cacheName);
    }
    
    // 3. Clear localStorage and sessionStorage
    localStorage.clear();
    sessionStorage.clear();
    console.log('✅ Cleared local storage');
    
    // 4. Force reload without cache
    console.log('🔄 Reloading page...');
    window.location.reload(true);
    
  } catch (error) {
    console.error('❌ Error clearing driver-connect cache:', error);
  }
}

// Run immediately
clearDriverConnectCache();