/**
 * Simple test to identify the sync issue
 * Run this in the browser console to debug the sync problem
 */

// Test function to debug the sync issue
window.debugSyncIssue = async function() {
  console.log('=== SYNC ISSUE DEBUG TEST ===');
  
  try {
    // Import the services
    const { backgroundSync } = await import('../services/backgroundSync.js');
    const { driverConnectOffline } = await import('../services/driverConnectOffline.js');
    const { offlineDB, SYNC_STATUS } = await import('../services/offlineDB.js');
    
    console.log('1. Services imported successfully');
    
    // Initialize database
    await offlineDB.initialize();
    console.log('2. Database initialized');
    
    // Check raw database data
    const allData = await offlineDB.getAllData('connectionQueue');
    console.log('3. Raw database data:', allData.length, 'items');
    
    if (allData.length > 0) {
      console.log('   First item:', {
        id: allData[0].id,
        status: allData[0].status,
        action: allData[0].action,
        hasApiPayload: !!allData[0].apiPayload
      });
    }
    
    // Check pending connections using the service method
    const pendingConnections = await driverConnectOffline.getPendingConnections();
    console.log('4. Pending connections via service:', pendingConnections.length, 'items');
    
    // Check pending count
    const pendingCount = await driverConnectOffline.getPendingCount();
    console.log('5. Pending count via service:', pendingCount);
    
    // Check if backgroundSync service is working
    console.log('6. Testing backgroundSync.syncDriverConnections()...');
    const syncResult = await backgroundSync.syncDriverConnections();
    console.log('7. Sync result:', syncResult);
    
    // Check if data was actually synced
    const pendingAfterSync = await driverConnectOffline.getPendingCount();
    console.log('8. Pending count after sync:', pendingAfterSync);
    
    return {
      allData: allData.length,
      pendingConnections: pendingConnections.length,
      pendingCount,
      syncResult,
      pendingAfterSync
    };
    
  } catch (error) {
    console.error('Debug test failed:', error);
    return { error: error.message };
  }
};

// Also add a function to inspect the current state
window.inspectSyncState = async function() {
  console.log('=== SYNC STATE INSPECTION ===');
  
  try {
    const { driverConnectOffline } = await import('../services/driverConnectOffline.js');
    const { offlineDB } = await import('../services/offlineDB.js');
    
    // Get all data
    const allData = await offlineDB.getAllData('connectionQueue');
    console.log('Total items in database:', allData.length);
    
    // Group by status
    const byStatus = {};
    allData.forEach(item => {
      byStatus[item.status] = (byStatus[item.status] || 0) + 1;
    });
    console.log('Items by status:', byStatus);
    
    // Check pending specifically
    const pending = await offlineDB.getDataByIndex('connectionQueue', 'status', 'pending');
    console.log('Pending items (direct query):', pending.length);
    
    // Check via service
    const servicePending = await driverConnectOffline.getPendingConnections();
    console.log('Pending items (via service):', servicePending.length);
    
    return {
      total: allData.length,
      byStatus,
      pendingDirect: pending.length,
      pendingService: servicePending.length,
      sampleData: allData.slice(0, 2)
    };
    
  } catch (error) {
    console.error('State inspection failed:', error);
    return { error: error.message };
  }
};

console.log('Sync debug functions loaded. Run:');
console.log('- window.debugSyncIssue() to test the full sync process');
console.log('- window.inspectSyncState() to inspect current data state');