/**
 * Debug script for offline data inspection
 * Use this to check what's stored in IndexedDB and troubleshoot sync issues
 */

import { offlineDB, SYNC_STATUS } from '../services/offlineDB.js';
import { driverConnectOffline } from '../services/driverConnectOffline.js';
import { backgroundSync } from '../services/backgroundSync.js';

export class OfflineDataDebug {
  
  // Check all stored data in IndexedDB
  static async inspectOfflineData() {
    console.log('=== OFFLINE DATA INSPECTION ===');
    
    try {
      await offlineDB.initialize();
      
      // Get all connection queue data
      const allConnections = await offlineDB.getAllData('connectionQueue');
      console.log(`Total connections in queue: ${allConnections.length}`);
      
      if (allConnections.length > 0) {
        console.log('Connection details:');
        allConnections.forEach((conn, index) => {
          console.log(`Connection ${index + 1}:`, {
            id: conn.id,
            status: conn.status,
            action: conn.action,
            employeeId: conn.employeeId,
            truckId: conn.truckId,
            priority: conn.priority,
            timestamp: conn.timestamp,
            hasApiPayload: !!conn.apiPayload,
            apiPayload: conn.apiPayload
          });
        });
      }
      
      // Get pending connections specifically
      const pendingConnections = await offlineDB.getAllPending('connectionQueue');
      console.log(`Pending connections: ${pendingConnections.length}`);
      
      // Get stats
      const stats = await offlineDB.getStats();
      console.log('Database stats:', stats);
      
      return {
        allConnections,
        pendingConnections,
        stats
      };
      
    } catch (error) {
      console.error('Failed to inspect offline data:', error);
      return null;
    }
  }
  
  // Test sync process step by step
  static async debugSyncProcess() {
    console.log('=== SYNC PROCESS DEBUG ===');
    
    try {
      // Step 1: Check pending connections
      const pendingConnections = await driverConnectOffline.getPendingConnections();
      console.log(`Step 1 - Found ${pendingConnections.length} pending connections`);
      
      if (pendingConnections.length === 0) {
        console.log('No pending connections to sync');
        return { success: false, message: 'No pending connections' };
      }
      
      // Step 2: Inspect first connection
      const firstConnection = pendingConnections[0];
      console.log('Step 2 - First connection details:', {
        id: firstConnection.id,
        status: firstConnection.status,
        hasApiPayload: !!firstConnection.apiPayload,
        apiPayload: firstConnection.apiPayload
      });
      
      // Step 3: Test API payload format
      const apiPayload = firstConnection.apiPayload || firstConnection.connectionData;
      console.log('Step 3 - API payload to send:', apiPayload);
      
      if (!apiPayload) {
        console.error('No API payload found in connection');
        return { success: false, message: 'No API payload' };
      }
      
      // Step 4: Test network connectivity
      console.log('Step 4 - Testing network connectivity...');
      try {
        const testResponse = await fetch('/api/health', { method: 'GET' });
        console.log('Network test result:', testResponse.status);
      } catch (networkError) {
        console.error('Network test failed:', networkError);
        return { success: false, message: 'Network connectivity issue' };
      }
      
      // Step 5: Test actual sync call
      console.log('Step 5 - Testing sync API call...');
      try {
        const response = await fetch('/api/driver/connect', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(apiPayload)
        });
        
        console.log('Sync API response status:', response.status);
        
        if (response.ok) {
          const result = await response.json();
          console.log('Sync API response data:', result);
          return { success: true, message: 'Sync test successful', result };
        } else {
          const errorText = await response.text();
          console.error('Sync API error response:', errorText);
          return { success: false, message: `Server error: ${response.status}`, error: errorText };
        }
        
      } catch (apiError) {
        console.error('Sync API call failed:', apiError);
        return { success: false, message: 'API call failed', error: apiError.message };
      }
      
    } catch (error) {
      console.error('Debug sync process failed:', error);
      return { success: false, message: 'Debug failed', error: error.message };
    }
  }
  
  // Test the full sync process
  static async testFullSync() {
    console.log('=== FULL SYNC TEST ===');
    
    try {
      const result = await backgroundSync.startSync();
      console.log('Full sync result:', result);
      return result;
    } catch (error) {
      console.error('Full sync test failed:', error);
      return { success: false, error: error.message };
    }
  }
  
  // Clear all offline data (for testing)
  static async clearAllOfflineData() {
    console.log('=== CLEARING ALL OFFLINE DATA ===');
    
    try {
      await driverConnectOffline.clearAllConnections();
      console.log('All offline data cleared');
      return { success: true, message: 'All data cleared' };
    } catch (error) {
      console.error('Failed to clear offline data:', error);
      return { success: false, error: error.message };
    }
  }
  
  // Add test connection data
  static async addTestConnection() {
    console.log('=== ADDING TEST CONNECTION ===');
    
    const testConnectionData = {
      action: 'check_in',
      driver_qr_data: {
        employee_id: 'TEST001',
        name: 'Test Driver',
        department: 'Transportation'
      },
      truck_qr_data: {
        id: 'TRUCK001',
        number: 'T001',
        license_plate: 'TEST-001'
      },
      timestamp: new Date().toISOString()
    };
    
    try {
      const result = await driverConnectOffline.storeConnection(testConnectionData);
      console.log('Test connection added:', result);
      return result;
    } catch (error) {
      console.error('Failed to add test connection:', error);
      return { success: false, error: error.message };
    }
  }
}

// Make it available globally for console debugging
if (typeof window !== 'undefined') {
  window.OfflineDataDebug = OfflineDataDebug;
}

export default OfflineDataDebug;