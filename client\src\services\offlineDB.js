// Hauling QR Trip Management System - Offline Database Schema
// Comprehensive IndexedDB management for PWA offline functionality

const DB_NAME = 'HaulingQROffline';
const DB_VERSION = 3;

// Sync status constants
export const SYNC_STATUS = {
  PENDING: 'pending',
  SYNCING: 'syncing',
  SYNCED: 'synced',
  FAILED: 'failed'
};

// Priority levels for sync operations
export const PRIORITY = {
  HIGH: 'high',
  MEDIUM: 'medium',
  LOW: 'low'
};

// Database schema definition
const STORES = {
  connectionQueue: {
    keyPath: 'id',
    autoIncrement: true,
    indexes: [
      { name: 'status', keyPath: 'status', unique: false },
      { name: 'priority', keyPath: 'priority', unique: false },
      { name: 'timestamp', keyPath: 'timestamp', unique: false },
      { name: 'employeeId', keyPath: 'employeeId', unique: false },
      { name: 'truckId', keyPath: 'truckId', unique: false }
    ]
  },
  conflicts: {
    keyPath: 'id',
    autoIncrement: true,
    indexes: [
      { name: 'status', keyPath: 'status', unique: false },
      { name: 'type', keyPath: 'type', unique: false },
      { name: 'timestamp', keyPath: 'timestamp', unique: false }
    ]
  },
  referenceData: {
    keyPath: 'type',
    autoIncrement: false,
    indexes: [
      { name: 'lastUpdated', keyPath: 'lastUpdated', unique: false }
    ]
  }
};

// Main offline database service
class OfflineDBService {
  constructor() {
    this.db = null;
    this.isInitialized = false;
  }

  // Initialize database connection
  async initialize() {
    if (this.isInitialized && this.db) {
      return this.db;
    }

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION);

      request.onerror = () => {
        console.error('[OfflineDB] Database connection failed:', request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        this.isInitialized = true;
        console.log('[OfflineDB] Database initialized successfully');
        resolve(this.db);
      };

      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        console.log('[OfflineDB] Database upgrade needed, creating stores...');

        // Create or update stores
        Object.entries(STORES).forEach(([storeName, config]) => {
          // Delete existing store if it exists
          if (db.objectStoreNames.contains(storeName)) {
            db.deleteObjectStore(storeName);
          }

          // Create new store
          const store = db.createObjectStore(storeName, {
            keyPath: config.keyPath,
            autoIncrement: config.autoIncrement
          });

          // Create indexes
          config.indexes.forEach(index => {
            store.createIndex(index.name, index.keyPath, { unique: index.unique });
          });

          console.log(`[OfflineDB] Created store: ${storeName}`);
        });
      };
    });
  }

  // Add data to a store
  async addData(storeName, data) {
    await this.initialize();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.add(data);

      request.onsuccess = () => {
        console.log(`[OfflineDB] Data added to ${storeName}:`, request.result);
        resolve(request.result);
      };

      request.onerror = () => {
        console.error(`[OfflineDB] Failed to add data to ${storeName}:`, request.error);
        reject(request.error);
      };
    });
  }

  // Update data in a store
  async updateData(storeName, data) {
    await this.initialize();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.put(data);

      request.onsuccess = () => {
        console.log(`[OfflineDB] Data updated in ${storeName}:`, request.result);
        resolve(request.result);
      };

      request.onerror = () => {
        console.error(`[OfflineDB] Failed to update data in ${storeName}:`, request.error);
        reject(request.error);
      };
    });
  }

  // Get data by ID
  async getData(storeName, id) {
    await this.initialize();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.get(id);

      request.onsuccess = () => {
        resolve(request.result);
      };

      request.onerror = () => {
        console.error(`[OfflineDB] Failed to get data from ${storeName}:`, request.error);
        reject(request.error);
      };
    });
  }

  // Get all data from a store
  async getAllData(storeName) {
    await this.initialize();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.getAll();

      request.onsuccess = () => {
        resolve(request.result || []);
      };

      request.onerror = () => {
        console.error(`[OfflineDB] Failed to get all data from ${storeName}:`, request.error);
        reject(request.error);
      };
    });
  }

  // Get data by index
  async getDataByIndex(storeName, indexName, value) {
    await this.initialize();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const index = store.index(indexName);
      const request = index.getAll(value);

      request.onsuccess = () => {
        resolve(request.result || []);
      };

      request.onerror = () => {
        console.error(`[OfflineDB] Failed to get data by index from ${storeName}:`, request.error);
        reject(request.error);
      };
    });
  }

  // Get all pending data (status = 'pending')
  async getAllPending(storeName) {
    return await this.getDataByIndex(storeName, 'status', SYNC_STATUS.PENDING);
  }

  // Delete data by ID
  async deleteData(storeName, id) {
    await this.initialize();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.delete(id);

      request.onsuccess = () => {
        console.log(`[OfflineDB] Data deleted from ${storeName}:`, id);
        resolve(true);
      };

      request.onerror = () => {
        console.error(`[OfflineDB] Failed to delete data from ${storeName}:`, request.error);
        reject(request.error);
      };
    });
  }

  // Clear all data from a store
  async clearStore(storeName) {
    await this.initialize();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.clear();

      request.onsuccess = () => {
        console.log(`[OfflineDB] Store ${storeName} cleared`);
        resolve(true);
      };

      request.onerror = () => {
        console.error(`[OfflineDB] Failed to clear store ${storeName}:`, request.error);
        reject(request.error);
      };
    });
  }

  // Get database statistics
  async getStats() {
    await this.initialize();
    
    const stats = {};
    
    for (const storeName of Object.keys(STORES)) {
      try {
        const allData = await this.getAllData(storeName);
        
        // Only try to get pending data for stores that have a status index
        let pendingData = [];
        let syncedCount = 0;
        let failedCount = 0;
        
        if (storeName === 'referenceData') {
          // referenceData doesn't use status field, so just count total
          stats[storeName] = {
            total: allData.length,
            pending: 0,
            synced: allData.length, // All reference data is considered "synced"
            failed: 0
          };
        } else {
          // For other stores that have status field
          try {
            pendingData = await this.getAllPending(storeName);
            syncedCount = allData.filter(item => item.status === SYNC_STATUS.SYNCED).length;
            failedCount = allData.filter(item => item.status === SYNC_STATUS.FAILED).length;
          } catch (indexError) {
            console.warn(`[OfflineDB] Status index not available for ${storeName}, using basic stats`);
            syncedCount = 0;
            failedCount = 0;
          }
          
          stats[storeName] = {
            total: allData.length,
            pending: pendingData.length,
            synced: syncedCount,
            failed: failedCount
          };
        }
      } catch (error) {
        console.error(`[OfflineDB] Failed to get stats for ${storeName}:`, error);
        stats[storeName] = { total: 0, pending: 0, synced: 0, failed: 0, error: error.message };
      }
    }
    
    return stats;
  }
}

// Conflict resolution service
class ConflictResolutionService {
  constructor(offlineDB) {
    this.offlineDB = offlineDB;
    this.storeName = 'conflicts';
  }

  // Detect and store conflict
  async detectConflict(localData, serverData, conflictType) {
    const conflict = {
      type: conflictType,
      status: 'pending',
      timestamp: new Date().toISOString(),
      localData: localData,
      serverData: serverData,
      resolution: null
    };

    const id = await this.offlineDB.addData(this.storeName, conflict);
    console.log(`[ConflictResolution] Conflict detected and stored:`, { id, type: conflictType });
    
    return { id, ...conflict };
  }

  // Get pending conflicts
  async getPendingConflicts() {
    return await this.offlineDB.getDataByIndex(this.storeName, 'status', 'pending');
  }

  // Auto-resolve conflict if possible
  async autoResolveConflict(conflictId) {
    const conflict = await this.offlineDB.getData(this.storeName, conflictId);
    
    if (!conflict) {
      console.warn(`[ConflictResolution] Conflict ${conflictId} not found`);
      return null;
    }

    // Simple auto-resolution logic - can be enhanced
    let resolution = null;
    
    if (conflict.type === 'duplicate') {
      // For duplicates, prefer server data
      resolution = {
        action: 'use_server',
        reason: 'Server data takes precedence for duplicates',
        resolvedAt: new Date().toISOString()
      };
    }

    if (resolution) {
      const updatedConflict = {
        ...conflict,
        status: 'resolved',
        resolution: resolution
      };
      
      await this.offlineDB.updateData(this.storeName, updatedConflict);
      console.log(`[ConflictResolution] Auto-resolved conflict ${conflictId}:`, resolution);
      
      return resolution;
    }

    return null;
  }
}

// Reference data caching service
class ReferenceDataService {
  constructor(offlineDB) {
    this.offlineDB = offlineDB;
    this.storeName = 'referenceData';
  }

  // Cache reference data
  async cacheReferenceData(type, data) {
    const cacheEntry = {
      type: type,
      data: data,
      lastUpdated: new Date().toISOString(),
      count: Array.isArray(data) ? data.length : 1
    };

    await this.offlineDB.updateData(this.storeName, cacheEntry);
    console.log(`[ReferenceData] Cached ${type} data:`, { count: cacheEntry.count });
  }

  // Get cached reference data
  async getCachedReferenceData(type) {
    return await this.offlineDB.getData(this.storeName, type);
  }

  // Check if reference data is stale
  async isReferenceDataStale(type, maxAgeHours = 24) {
    const cached = await this.getCachedReferenceData(type);
    
    if (!cached) return true;
    
    const lastUpdated = new Date(cached.lastUpdated);
    const now = new Date();
    const ageHours = (now - lastUpdated) / (1000 * 60 * 60);
    
    return ageHours > maxAgeHours;
  }
}

// Create singleton instances
export const offlineDB = new OfflineDBService();
export const conflictResolution = new ConflictResolutionService(offlineDB);
export const referenceData = new ReferenceDataService(offlineDB);

// Export main service
export default offlineDB;