/**
 * PWA-Only Offline Functionality Test Suite
 * 
 * This test suite validates that:
 * 1. Browser mode shows offline errors for driver-connect routes
 * 2. PWA mode serves cached content for driver-connect routes
 * 3. PWA mode detection accuracy across different scenarios
 * 4. Service worker correctly receives and uses PWA mode status
 * 
 * Requirements: 1.1, 1.2, 1.3, 1.4, 5.1, 5.2, 5.3, 5.4
 */

import { render, screen, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { usePWAStatus } from '../hooks/usePWAStatus';
import React from 'react';

// Mock service worker registration
const mockServiceWorker = {
  controller: {
    postMessage: jest.fn()
  },
  ready: Promise.resolve({
    active: {
      postMessage: jest.fn()
    }
  }),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  getRegistration: jest.fn()
};

// Mock navigator
const mockNavigator = {
  serviceWorker: mockServiceWorker,
  onLine: true,
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
};

// Mock window.matchMedia
const mockMatchMedia = (query) => ({
  matches: query === '(display-mode: standalone)' ? false : false,
  media: query,
  onchange: null,
  addListener: jest.fn(),
  removeListener: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  dispatchEvent: jest.fn(),
});

// Test component that uses PWA status hook
const TestPWAComponent = ({ onPWAStatusChange }) => {
  const pwaStatus = usePWAStatus();
  
  React.useEffect(() => {
    if (onPWAStatusChange) {
      onPWAStatusChange(pwaStatus);
    }
  }, [pwaStatus, onPWAStatusChange]);
  
  return (
    <div data-testid="pwa-test-component">
      <div data-testid="pwa-mode">{pwaStatus.isPWA ? 'PWA' : 'Browser'}</div>
      <div data-testid="online-status">{pwaStatus.isOnline ? 'Online' : 'Offline'}</div>
      <div data-testid="sync-status">{pwaStatus.syncStatus}</div>
    </div>
  );
};

describe('PWA-Only Offline Functionality Tests', () => {
  let originalNavigator;
  let originalMatchMedia;
  let originalFetch;
  let mockFetch;

  beforeEach(() => {
    // Store originals
    originalNavigator = global.navigator;
    originalMatchMedia = global.window.matchMedia;
    originalFetch = global.fetch;

    // Setup mocks
    global.navigator = { ...originalNavigator, ...mockNavigator };
    global.window.matchMedia = mockMatchMedia;
    
    mockFetch = jest.fn();
    global.fetch = mockFetch;

    // Clear all mocks
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Restore originals
    global.navigator = originalNavigator;
    global.window.matchMedia = originalMatchMedia;
    global.fetch = originalFetch;
  });

  describe('Requirement 1.1 & 1.2: Browser vs PWA Mode Offline Behavior', () => {
    test('Browser mode should allow normal fetch failures for driver-connect routes', async () => {
      // Setup browser mode
      global.window.matchMedia = jest.fn().mockImplementation((query) => ({
        matches: query === '(display-mode: standalone)' ? false : false,
        media: query,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
      }));

      // Mock fetch to simulate network failure
      mockFetch.mockRejectedValue(new Error('Network error'));

      let pwaStatus;
      render(
        <BrowserRouter>
          <TestPWAComponent onPWAStatusChange={(status) => { pwaStatus = status; }} />
        </BrowserRouter>
      );

      await waitFor(() => {
        expect(pwaStatus?.isPWA).toBe(false);
      });

      // Verify service worker receives browser mode status
      expect(mockServiceWorker.controller.postMessage).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'PWA_MODE_STATUS',
          isPWA: false
        })
      );
    });

    test('PWA mode should be detected correctly', async () => {
      // Setup PWA mode
      global.window.matchMedia = jest.fn().mockImplementation((query) => ({
        matches: query === '(display-mode: standalone)' ? true : false,
        media: query,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
      }));

      let pwaStatus;
      render(
        <BrowserRouter>
          <TestPWAComponent onPWAStatusChange={(status) => { pwaStatus = status; }} />
        </BrowserRouter>
      );

      await waitFor(() => {
        expect(pwaStatus?.isPWA).toBe(true);
      });

      // Verify service worker receives PWA mode status
      expect(mockServiceWorker.controller.postMessage).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'PWA_MODE_STATUS',
          isPWA: true
        })
      );
    });
  });

  describe('Requirement 1.3 & 1.4: Service Worker PWA Mode Detection', () => {
    test('Service worker should receive PWA mode status updates', async () => {
      const mockPostMessage = jest.fn();
      global.navigator.serviceWorker.controller.postMessage = mockPostMessage;

      render(
        <BrowserRouter>
          <TestPWAComponent />
        </BrowserRouter>
      );

      await waitFor(() => {
        expect(mockPostMessage).toHaveBeenCalledWith(
          expect.objectContaining({
            type: 'PWA_MODE_STATUS',
            isPWA: expect.any(Boolean),
            currentPath: expect.any(String),
            timestamp: expect.any(String),
            source: 'pwa-status-hook'
          })
        );
      });
    });

    test('Service worker should receive location change notifications', async () => {
      const mockPostMessage = jest.fn();
      global.navigator.serviceWorker.controller.postMessage = mockPostMessage;

      const { rerender } = render(
        <BrowserRouter>
          <TestPWAComponent />
        </BrowserRouter>
      );

      // Clear initial calls
      mockPostMessage.mockClear();

      // Simulate location change by re-rendering
      rerender(
        <BrowserRouter>
          <TestPWAComponent />
        </BrowserRouter>
      );

      await waitFor(() => {
        expect(mockPostMessage).toHaveBeenCalledWith(
          expect.objectContaining({
            type: 'LOCATION_CHANGE',
            currentPath: expect.any(String),
            isPWA: expect.any(Boolean),
            source: 'pwa-status-hook'
          })
        );
      });
    });
  });

  describe('Requirement 5.1 & 5.2: PWA Mode Detection Accuracy', () => {
    test('Should detect PWA mode using display-mode media query', () => {
      global.window.matchMedia = jest.fn().mockImplementation((query) => ({
        matches: query === '(display-mode: standalone)' ? true : false,
        media: query,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
      }));

      let pwaStatus;
      render(
        <BrowserRouter>
          <TestPWAComponent onPWAStatusChange={(status) => { pwaStatus = status; }} />
        </BrowserRouter>
      );

      expect(pwaStatus?.isPWA).toBe(true);
    });

    test('Should detect PWA mode using iOS navigator.standalone', () => {
      global.navigator.standalone = true;
      global.window.matchMedia = jest.fn().mockImplementation((query) => ({
        matches: false,
        media: query,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
      }));

      let pwaStatus;
      render(
        <BrowserRouter>
          <TestPWAComponent onPWAStatusChange={(status) => { pwaStatus = status; }} />
        </BrowserRouter>
      );

      expect(pwaStatus?.isPWA).toBe(true);
    });

    test('Should detect PWA mode using Android app referrer', () => {
      // Mock document.referrer
      Object.defineProperty(document, 'referrer', {
        value: 'android-app://com.example.app',
        configurable: true
      });

      global.window.matchMedia = jest.fn().mockImplementation((query) => ({
        matches: false,
        media: query,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
      }));

      let pwaStatus;
      render(
        <BrowserRouter>
          <TestPWAComponent onPWAStatusChange={(status) => { pwaStatus = status; }} />
        </BrowserRouter>
      );

      expect(pwaStatus?.isPWA).toBe(true);
    });

    test('Should default to browser mode when no PWA indicators are present', () => {
      global.navigator.standalone = false;
      global.window.matchMedia = jest.fn().mockImplementation((query) => ({
        matches: false,
        media: query,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
      }));

      Object.defineProperty(document, 'referrer', {
        value: 'https://example.com',
        configurable: true
      });

      let pwaStatus;
      render(
        <BrowserRouter>
          <TestPWAComponent onPWAStatusChange={(status) => { pwaStatus = status; }} />
        </BrowserRouter>
      );

      expect(pwaStatus?.isPWA).toBe(false);
    });
  });

  describe('Requirement 5.3 & 5.4: Service Worker Communication', () => {
    test('Should handle service worker PWA mode requests', async () => {
      const mockAddEventListener = jest.fn();
      global.navigator.serviceWorker.addEventListener = mockAddEventListener;

      render(
        <BrowserRouter>
          <TestPWAComponent />
        </BrowserRouter>
      );

      // Verify event listener was added for service worker messages
      expect(mockAddEventListener).toHaveBeenCalledWith(
        'message',
        expect.any(Function)
      );

      // Simulate service worker requesting PWA mode
      const messageHandler = mockAddEventListener.mock.calls.find(
        call => call[0] === 'message'
      )[1];

      const mockEvent = {
        data: {
          type: 'REQUEST_PWA_MODE',
          timestamp: new Date().toISOString(),
          requestId: 'test-request-123'
        }
      };

      // Call the message handler
      messageHandler(mockEvent);

      // Verify PWA mode status is sent in response
      await waitFor(() => {
        expect(mockServiceWorker.controller.postMessage).toHaveBeenCalledWith(
          expect.objectContaining({
            type: 'PWA_MODE_STATUS',
            isPWA: expect.any(Boolean)
          })
        );
      });
    });

    test('Should send client ready notification to service worker', async () => {
      const mockPostMessage = jest.fn();
      global.navigator.serviceWorker.controller.postMessage = mockPostMessage;

      render(
        <BrowserRouter>
          <TestPWAComponent />
        </BrowserRouter>
      );

      await waitFor(() => {
        expect(mockPostMessage).toHaveBeenCalledWith(
          expect.objectContaining({
            type: 'CLIENT_READY',
            source: 'pwa-status-hook'
          })
        );
      });
    });

    test('Should handle service worker ready confirmation', async () => {
      const mockAddEventListener = jest.fn();
      global.navigator.serviceWorker.addEventListener = mockAddEventListener;

      render(
        <BrowserRouter>
          <TestPWAComponent />
        </BrowserRouter>
      );

      const messageHandler = mockAddEventListener.mock.calls.find(
        call => call[0] === 'message'
      )[1];

      const mockEvent = {
        data: {
          type: 'SERVICE_WORKER_READY',
          timestamp: new Date().toISOString(),
          currentPWAMode: true,
          source: 'service-worker'
        }
      };

      // Should not throw error when handling service worker ready message
      expect(() => messageHandler(mockEvent)).not.toThrow();
    });
  });

  describe('Integration Tests: End-to-End PWA Mode Detection', () => {
    test('Should maintain PWA mode consistency across location changes', async () => {
      // Setup PWA mode
      global.window.matchMedia = jest.fn().mockImplementation((query) => ({
        matches: query === '(display-mode: standalone)' ? true : false,
        media: query,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
      }));

      const mockPostMessage = jest.fn();
      global.navigator.serviceWorker.controller.postMessage = mockPostMessage;

      let pwaStatus;
      const { rerender } = render(
        <BrowserRouter>
          <TestPWAComponent onPWAStatusChange={(status) => { pwaStatus = status; }} />
        </BrowserRouter>
      );

      // Verify initial PWA mode
      await waitFor(() => {
        expect(pwaStatus?.isPWA).toBe(true);
      });

      // Clear previous calls
      mockPostMessage.mockClear();

      // Simulate location change
      rerender(
        <BrowserRouter>
          <TestPWAComponent onPWAStatusChange={(status) => { pwaStatus = status; }} />
        </BrowserRouter>
      );

      // Verify PWA mode is maintained and communicated
      await waitFor(() => {
        expect(pwaStatus?.isPWA).toBe(true);
        expect(mockPostMessage).toHaveBeenCalledWith(
          expect.objectContaining({
            type: 'LOCATION_CHANGE',
            isPWA: true
          })
        );
      });
    });

    test('Should handle service worker communication errors gracefully', async () => {
      // Setup service worker that throws errors
      const mockPostMessage = jest.fn().mockImplementation(() => {
        throw new Error('Service worker communication failed');
      });
      global.navigator.serviceWorker.controller.postMessage = mockPostMessage;

      // Should not throw error even if service worker communication fails
      expect(() => {
        render(
          <BrowserRouter>
            <TestPWAComponent />
          </BrowserRouter>
        );
      }).not.toThrow();
    });

    test('Should work when service worker is not available', () => {
      // Remove service worker support
      global.navigator.serviceWorker = undefined;

      let pwaStatus;
      expect(() => {
        render(
          <BrowserRouter>
            <TestPWAComponent onPWAStatusChange={(status) => { pwaStatus = status; }} />
          </BrowserRouter>
        );
      }).not.toThrow();

      // PWA detection should still work without service worker
      expect(pwaStatus?.isPWA).toBeDefined();
    });
  });

  describe('Error Handling and Edge Cases', () => {
    test('Should handle malformed service worker messages', async () => {
      const mockAddEventListener = jest.fn();
      global.navigator.serviceWorker.addEventListener = mockAddEventListener;

      render(
        <BrowserRouter>
          <TestPWAComponent />
        </BrowserRouter>
      );

      const messageHandler = mockAddEventListener.mock.calls.find(
        call => call[0] === 'message'
      )[1];

      // Test with malformed message
      const malformedEvent = {
        data: null
      };

      expect(() => messageHandler(malformedEvent)).not.toThrow();

      // Test with invalid message type
      const invalidEvent = {
        data: {
          type: 'INVALID_MESSAGE_TYPE',
          someData: 'test'
        }
      };

      expect(() => messageHandler(invalidEvent)).not.toThrow();
    });

    test('Should handle media query errors gracefully', () => {
      // Mock matchMedia to throw error
      global.window.matchMedia = jest.fn().mockImplementation(() => {
        throw new Error('Media query not supported');
      });

      let pwaStatus;
      expect(() => {
        render(
          <BrowserRouter>
            <TestPWAComponent onPWAStatusChange={(status) => { pwaStatus = status; }} />
          </BrowserRouter>
        );
      }).not.toThrow();

      // Should default to browser mode on error
      expect(pwaStatus?.isPWA).toBe(false);
    });
  });
});