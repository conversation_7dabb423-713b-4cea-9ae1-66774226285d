import React, { useState, useEffect } from 'react';
import { driverConnectOffline } from '../../services/driverConnectOffline';
import { backgroundSync } from '../../services/backgroundSync';
import { offlineDB } from '../../services/offlineDB';
import { DatabaseReset } from '../../utils/databaseReset';

/**
 * Debug panel for troubleshooting sync issues
 * Shows detailed information about offline data and sync process
 */
const SyncDebugPanel = ({ isVisible, onClose }) => {
  const [debugData, setDebugData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [logs, setLogs] = useState([]);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { timestamp, message, type }]);
  };

  const inspectOfflineData = async () => {
    setLoading(true);
    addLog('Starting offline data inspection...', 'info');

    try {
      // Initialize database
      try {
        await offlineDB.initialize();
        addLog('Database initialized successfully', 'success');
      } catch (dbError) {
        if (dbError.name === 'VersionError' || dbError.message.includes('version')) {
          addLog(`Database version error detected: ${dbError.message}`, 'error');
          addLog('This is likely causing the sync issue. Use "Reset Database" to fix.', 'warning');
          return;
        } else {
          throw dbError;
        }
      }

      // Get all connection data
      const allConnections = await offlineDB.getAllData('connectionQueue');
      addLog(`Found ${allConnections.length} total connections in queue`, 'info');

      // Get pending connections specifically
      const pendingConnections = await driverConnectOffline.getPendingConnections();
      addLog(`Found ${pendingConnections.length} pending connections`, 'info');

      // Get pending count
      const pendingCount = await driverConnectOffline.getPendingCount();
      addLog(`Pending count method returns: ${pendingCount}`, 'info');

      // Get stats
      const stats = await offlineDB.getStats();
      addLog('Database stats retrieved', 'info');

      // Inspect first connection if available
      let firstConnectionDetails = null;
      if (allConnections.length > 0) {
        const firstConn = allConnections[0];
        firstConnectionDetails = {
          id: firstConn.id,
          status: firstConn.status,
          action: firstConn.action,
          employeeId: firstConn.employeeId,
          truckId: firstConn.truckId,
          priority: firstConn.priority,
          timestamp: firstConn.timestamp,
          hasApiPayload: !!firstConn.apiPayload,
          apiPayloadKeys: firstConn.apiPayload ? Object.keys(firstConn.apiPayload) : [],
          syncMetadata: firstConn.syncMetadata
        };
        addLog(`First connection details: ID=${firstConn.id}, Status=${firstConn.status}`, 'info');
      }

      setDebugData({
        allConnections: allConnections.length,
        pendingConnections: pendingConnections.length,
        pendingCount,
        stats,
        firstConnectionDetails,
        rawConnections: allConnections.slice(0, 3) // First 3 for inspection
      });

      addLog('Offline data inspection completed', 'success');

    } catch (error) {
      addLog(`Error inspecting offline data: ${error.message}`, 'error');
      console.error('Debug inspection error:', error);
    } finally {
      setLoading(false);
    }
  };

  const testSyncProcess = async () => {
    setLoading(true);
    addLog('Starting sync process test...', 'info');

    try {
      // Step 1: Check pending connections
      const pendingConnections = await driverConnectOffline.getPendingConnections();
      addLog(`Step 1: Found ${pendingConnections.length} pending connections`, 'info');

      if (pendingConnections.length === 0) {
        addLog('No pending connections to sync', 'warning');
        setLoading(false);
        return;
      }

      // Step 2: Test first connection
      const firstConnection = pendingConnections[0];
      addLog(`Step 2: Testing connection ID ${firstConnection.id}`, 'info');

      const apiPayload = firstConnection.apiPayload;
      if (!apiPayload) {
        addLog('No API payload found in connection', 'error');
        return;
      }

      addLog(`API payload keys: ${Object.keys(apiPayload).join(', ')}`, 'info');

      // Step 3: Test network connectivity
      addLog('Step 3: Testing network connectivity...', 'info');
      try {
        const testResponse = await fetch('/api/health', { method: 'GET' });
        addLog(`Network test: ${testResponse.status}`, testResponse.ok ? 'success' : 'error');
      } catch (networkError) {
        addLog(`Network test failed: ${networkError.message}`, 'error');
        return;
      }

      // Step 4: Test actual sync call
      addLog('Step 4: Testing sync API call...', 'info');
      try {
        const response = await fetch('/api/driver/connect', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(apiPayload)
        });

        addLog(`Sync API response status: ${response.status}`, response.ok ? 'success' : 'error');

        if (response.ok) {
          const result = await response.json();
          addLog(`Sync API response: ${JSON.stringify(result, null, 2)}`, 'success');
          
          if (result.success) {
            addLog('API call successful - connection should sync', 'success');
          } else {
            addLog('API call returned success=false', 'warning');
          }
        } else {
          const errorText = await response.text();
          addLog(`Sync API error: ${errorText}`, 'error');
        }

      } catch (apiError) {
        addLog(`Sync API call failed: ${apiError.message}`, 'error');
      }

    } catch (error) {
      addLog(`Sync test error: ${error.message}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  const testDriverConnectionSync = async () => {
    setLoading(true);
    addLog('Starting driver connection sync test...', 'info');

    try {
      const result = await backgroundSync.syncDriverConnections();
      addLog(`Driver connection sync completed`, 'info');
      addLog(`Sync results: ${JSON.stringify(result, null, 2)}`, 'info');

      if (result) {
        const { total, synced, failed, conflicts } = result;
        addLog(`Driver connections: ${synced}/${total} synced, ${failed} failed, ${conflicts} conflicts`, 
               synced > 0 ? 'success' : 'warning');
        
        if (synced > 0) {
          addLog('✅ Driver connection sync is working!', 'success');
        } else if (total === 0) {
          addLog('ℹ️ No pending connections to sync', 'info');
        } else {
          addLog('❌ Driver connections failed to sync', 'error');
        }
      }

    } catch (error) {
      addLog(`Driver connection sync failed: ${error.message}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  const testFullSync = async () => {
    setLoading(true);
    addLog('Starting full sync test...', 'info');

    try {
      const result = await backgroundSync.startSync();
      addLog(`Full sync completed`, 'info');
      addLog(`Sync results: ${JSON.stringify(result, null, 2)}`, 'info');

      if (result.driverConnections) {
        const { total, synced, failed, conflicts } = result.driverConnections;
        addLog(`Driver connections: ${synced}/${total} synced, ${failed} failed, ${conflicts} conflicts`, 
               synced > 0 ? 'success' : 'warning');
      }

    } catch (error) {
      addLog(`Full sync failed: ${error.message}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const clearOfflineData = async () => {
    if (window.confirm('Are you sure you want to clear all offline data? This cannot be undone.')) {
      try {
        await driverConnectOffline.clearAllConnections();
        addLog('All offline data cleared', 'success');
        setDebugData(null);
      } catch (error) {
        addLog(`Failed to clear offline data: ${error.message}`, 'error');
      }
    }
  };

  const resetDatabase = async () => {
    if (window.confirm('Are you sure you want to reset the entire database? This will fix version conflicts but delete all offline data.')) {
      setLoading(true);
      addLog('Starting database reset...', 'info');
      
      try {
        const result = await DatabaseReset.resetAndReinitialize();
        if (result.success) {
          addLog('Database reset successfully', 'success');
          setDebugData(null);
          // Refresh the page to ensure clean state
          setTimeout(() => {
            window.location.reload();
          }, 2000);
        } else {
          addLog(`Database reset failed: ${result.error}`, 'error');
        }
      } catch (error) {
        addLog(`Database reset error: ${error.message}`, 'error');
      } finally {
        setLoading(false);
      }
    }
  };

  const checkDatabaseVersion = async () => {
    addLog('Checking database version...', 'info');
    try {
      const version = await DatabaseReset.checkDatabaseVersion();
      addLog(`Current database version: ${version}`, 'info');
      
      const databases = await DatabaseReset.listDatabases();
      if (databases.length > 0) {
        addLog(`All databases: ${databases.map(db => `${db.name} (v${db.version})`).join(', ')}`, 'info');
      }
    } catch (error) {
      addLog(`Failed to check database version: ${error.message}`, 'error');
    }
  };

  useEffect(() => {
    if (isVisible) {
      inspectOfflineData();
    }
  }, [isVisible]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <div className="bg-gray-800 text-white px-6 py-4 flex justify-between items-center">
          <h2 className="text-xl font-semibold">Sync Debug Panel</h2>
          <button
            onClick={onClose}
            className="text-gray-300 hover:text-white text-2xl"
          >
            ×
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
          {/* Action Buttons */}
          <div className="mb-6 flex flex-wrap gap-2">
            <button
              onClick={inspectOfflineData}
              disabled={loading}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
            >
              Inspect Offline Data
            </button>
            <button
              onClick={testSyncProcess}
              disabled={loading}
              className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50"
            >
              Test Sync Process
            </button>
            <button
              onClick={testDriverConnectionSync}
              disabled={loading}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
            >
              Test Driver Sync Only
            </button>
            <button
              onClick={testFullSync}
              disabled={loading}
              className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 disabled:opacity-50"
            >
              Test Full Sync
            </button>
            <button
              onClick={clearLogs}
              className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
            >
              Clear Logs
            </button>
            <button
              onClick={clearOfflineData}
              className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
            >
              Clear Offline Data
            </button>
            <button
              onClick={checkDatabaseVersion}
              className="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600"
            >
              Check DB Version
            </button>
            <button
              onClick={resetDatabase}
              disabled={loading}
              className="bg-red-700 text-white px-4 py-2 rounded hover:bg-red-800 disabled:opacity-50"
            >
              Reset Database
            </button>
          </div>

          {/* Debug Data Display */}
          {debugData && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-3">Offline Data Summary</h3>
              <div className="bg-gray-100 p-4 rounded">
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div>
                    <strong>Total Connections:</strong> {debugData.allConnections}
                  </div>
                  <div>
                    <strong>Pending Connections:</strong> {debugData.pendingConnections}
                  </div>
                  <div>
                    <strong>Pending Count Method:</strong> {debugData.pendingCount}
                  </div>
                  <div>
                    <strong>Database Stats:</strong> {JSON.stringify(debugData.stats.connectionQueue || {})}
                  </div>
                </div>

                {debugData.firstConnectionDetails && (
                  <div>
                    <h4 className="font-semibold mb-2">First Connection Details:</h4>
                    <pre className="bg-white p-2 rounded text-sm overflow-x-auto">
                      {JSON.stringify(debugData.firstConnectionDetails, null, 2)}
                    </pre>
                  </div>
                )}

                {debugData.rawConnections && debugData.rawConnections.length > 0 && (
                  <div className="mt-4">
                    <h4 className="font-semibold mb-2">Raw Connection Data (First 3):</h4>
                    <pre className="bg-white p-2 rounded text-sm overflow-x-auto max-h-40">
                      {JSON.stringify(debugData.rawConnections, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Logs Display */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Debug Logs</h3>
            <div className="bg-black text-green-400 p-4 rounded font-mono text-sm max-h-60 overflow-y-auto">
              {logs.length === 0 ? (
                <div className="text-gray-500">No logs yet...</div>
              ) : (
                logs.map((log, index) => (
                  <div key={index} className={`mb-1 ${
                    log.type === 'error' ? 'text-red-400' :
                    log.type === 'success' ? 'text-green-400' :
                    log.type === 'warning' ? 'text-yellow-400' :
                    'text-gray-300'
                  }`}>
                    [{log.timestamp}] {log.message}
                  </div>
                ))
              )}
            </div>
          </div>

          {loading && (
            <div className="mt-4 text-center">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              <div className="mt-2 text-gray-600">Processing...</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SyncDebugPanel;