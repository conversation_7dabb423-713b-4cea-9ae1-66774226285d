/**
 * Standalone sync test script
 * Run this in the browser console to test sync functionality
 */

// Import required services
import { driverConnectOffline } from '../services/driverConnectOffline.js';
import { backgroundSync } from '../services/backgroundSync.js';
import { offlineDB } from '../services/offlineDB.js';

export async function testSyncIssue() {
  console.log('=== SYNC ISSUE DIAGNOSTIC TEST ===');
  
  try {
    // Step 1: Initialize database
    console.log('Step 1: Initializing database...');
    await offlineDB.initialize();
    console.log('✅ Database initialized');
    
    // Step 2: Check current offline data
    console.log('Step 2: Checking offline data...');
    const allConnections = await offlineDB.getAllData('connectionQueue');
    console.log(`Found ${allConnections.length} total connections`);
    
    const pendingConnections = await driverConnectOffline.getPendingConnections();
    console.log(`Found ${pendingConnections.length} pending connections`);
    
    const pendingCount = await driverConnectOffline.getPendingCount();
    console.log(`Pending count method returns: ${pendingCount}`);
    
    // Step 3: Inspect first connection if available
    if (allConnections.length > 0) {
      console.log('Step 3: Inspecting first connection...');
      const firstConn = allConnections[0];
      console.log('First connection details:', {
        id: firstConn.id,
        status: firstConn.status,
        action: firstConn.action,
        employeeId: firstConn.employeeId,
        truckId: firstConn.truckId,
        hasApiPayload: !!firstConn.apiPayload,
        apiPayload: firstConn.apiPayload
      });
      
      // Step 4: Test API payload format
      if (firstConn.apiPayload) {
        console.log('Step 4: Testing API payload format...');
        console.log('API payload keys:', Object.keys(firstConn.apiPayload));
        console.log('API payload:', firstConn.apiPayload);
        
        // Step 5: Test network call
        console.log('Step 5: Testing network call...');
        try {
          const response = await fetch('/api/driver/connect', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(firstConn.apiPayload)
          });
          
          console.log(`Network response status: ${response.status}`);
          
          if (response.ok) {
            const result = await response.json();
            console.log('Network response data:', result);
            
            if (result.success) {
              console.log('✅ API call successful - connection should sync');
            } else {
              console.log('⚠️ API call returned success=false:', result.message);
            }
          } else {
            const errorText = await response.text();
            console.log('❌ API call failed:', errorText);
          }
          
        } catch (networkError) {
          console.log('❌ Network error:', networkError.message);
        }
      } else {
        console.log('❌ No API payload found in connection');
      }
    } else {
      console.log('No connections found to test');
    }
    
    // Step 6: Test full sync process
    console.log('Step 6: Testing full sync process...');
    try {
      const syncResult = await backgroundSync.startSync();
      console.log('Sync result:', syncResult);
      
      if (syncResult.driverConnections) {
        const { total, synced, failed, conflicts } = syncResult.driverConnections;
        console.log(`Sync summary: ${synced}/${total} synced, ${failed} failed, ${conflicts} conflicts`);
        
        if (synced === 0 && total > 0) {
          console.log('🔍 ISSUE IDENTIFIED: Sync found connections but synced 0 items');
        } else if (synced > 0) {
          console.log('✅ Sync working correctly');
        }
      }
      
    } catch (syncError) {
      console.log('❌ Sync process failed:', syncError.message);
    }
    
    console.log('=== DIAGNOSTIC TEST COMPLETE ===');
    
  } catch (error) {
    console.error('Diagnostic test failed:', error);
  }
}

// Make it available globally
if (typeof window !== 'undefined') {
  window.testSyncIssue = testSyncIssue;
}

export default testSyncIssue;