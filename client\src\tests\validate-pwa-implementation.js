/**
 * PWA Offline Functionality Implementation Validator
 * 
 * This script validates that the PWA-only offline functionality is properly implemented
 * by checking the actual code files for required features.
 * 
 * Requirements: 1.1, 1.2, 1.3, 1.4, 5.1, 5.2, 5.3, 5.4
 */

const fs = require('fs');
const path = require('path');

class PWAImplementationValidator {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      total: 0,
      details: []
    };
  }

  log(level, message, data = null) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
    
    console.log(logMessage);
    if (data) {
      console.log(JSON.stringify(data, null, 2));
    }
  }

  recordTest(testName, passed, details = null) {
    this.results.total++;
    if (passed) {
      this.results.passed++;
    } else {
      this.results.failed++;
    }
    
    this.results.details.push({
      test: testName,
      passed,
      details,
      timestamp: new Date().toISOString()
    });
    
    this.log(passed ? 'info' : 'error', `${testName}: ${passed ? 'PASSED' : 'FAILED'}`, details);
  }

  validateServiceWorkerImplementation() {
    this.log('info', 'Validating service worker PWA mode implementation');
    
    const swPath = path.join(__dirname, '..', '..', 'public', 'sw.js');
    
    try {
      const swContent = fs.readFileSync(swPath, 'utf8');
      
      // Test 1: PWA mode detection variables
      const hasPWAModeVariable = /let clientPWAMode\s*=/.test(swContent);
      this.recordTest('SW: PWA Mode Variable Declaration', hasPWAModeVariable);
      
      const hasPWAStatusTimestamp = /pwaStatusLastUpdated/.test(swContent);
      this.recordTest('SW: PWA Status Timestamp Tracking', hasPWAStatusTimestamp);
      
      // Test 2: Message handling for PWA mode status
      const hasPWAModeStatusHandler = /PWA_MODE_STATUS/.test(swContent);
      this.recordTest('SW: PWA Mode Status Message Handler', hasPWAModeStatusHandler);
      
      const hasLocationChangeHandler = /LOCATION_CHANGE/.test(swContent);
      this.recordTest('SW: Location Change Message Handler', hasLocationChangeHandler);
      
      // Test 3: PWA mode detection function
      const hasDetectPWAModeFunction = /detectPWAMode/.test(swContent);
      this.recordTest('SW: PWA Mode Detection Function', hasDetectPWAModeFunction);
      
      const hasClientCommunication = /clients\.matchAll/.test(swContent);
      this.recordTest('SW: Client Communication Logic', hasClientCommunication);
      
      // Test 4: Navigation handling with PWA mode check
      const hasNavigationHandler = /handleNavigation/.test(swContent);
      this.recordTest('SW: Navigation Handler Function', hasNavigationHandler);
      
      const hasDriverConnectCheck = /driver-connect/.test(swContent);
      this.recordTest('SW: Driver Connect Route Check', hasDriverConnectCheck);
      
      const hasPWAModeRouting = /isPWAMode/.test(swContent);
      this.recordTest('SW: PWA Mode Routing Logic', hasPWAModeRouting);
      
      // Test 5: Enhanced message handling
      const hasMessageListener = /addEventListener.*message/.test(swContent);
      this.recordTest('SW: Message Event Listener', hasMessageListener);
      
      const hasRequestPWAMode = /REQUEST_PWA_MODE/.test(swContent);
      this.recordTest('SW: PWA Mode Request Handler', hasRequestPWAMode);
      
      return true;
      
    } catch (error) {
      this.recordTest('SW: File Access', false, { error: error.message });
      return false;
    }
  }

  validatePWAStatusHookImplementation() {
    this.log('info', 'Validating PWA status hook implementation');
    
    const hookPath = path.join(__dirname, '..', 'hooks', 'usePWAStatus.js');
    
    try {
      const hookContent = fs.readFileSync(hookPath, 'utf8');
      
      // Test 1: PWA mode detection methods
      const hasDisplayModeDetection = /display-mode.*standalone/.test(hookContent);
      this.recordTest('Hook: Display Mode Detection', hasDisplayModeDetection);
      
      const hasIOSStandaloneDetection = /navigator\.standalone/.test(hookContent);
      this.recordTest('Hook: iOS Standalone Detection', hasIOSStandaloneDetection);
      
      const hasAndroidAppDetection = /android-app/.test(hookContent);
      this.recordTest('Hook: Android App Detection', hasAndroidAppDetection);
      
      // Test 2: Service worker communication
      const hasServiceWorkerCommunication = /serviceWorker\.controller\.postMessage/.test(hookContent);
      this.recordTest('Hook: Service Worker Communication', hasServiceWorkerCommunication);
      
      const hasPWAModeStatusMessage = /PWA_MODE_STATUS/.test(hookContent);
      this.recordTest('Hook: PWA Mode Status Message', hasPWAModeStatusMessage);
      
      const hasLocationChangeMessage = /LOCATION_CHANGE/.test(hookContent);
      this.recordTest('Hook: Location Change Message', hasLocationChangeMessage);
      
      // Test 3: Enhanced PWA mode detection function
      const hasDetectAndSendFunction = /detectAndSendPWAMode/.test(hookContent);
      this.recordTest('Hook: Detect and Send PWA Mode Function', hasDetectAndSendFunction);
      
      const hasMultipleDetectionMethods = /detectionMethods/.test(hookContent);
      this.recordTest('Hook: Multiple Detection Methods', hasMultipleDetectionMethods);
      
      // Test 4: Error handling
      const hasErrorHandling = /try.*catch|catch.*error|error.*handling/i.test(hookContent);
      this.recordTest('Hook: Error Handling', hasErrorHandling);
      
      const hasFallbackDetection = /fallback/.test(hookContent);
      this.recordTest('Hook: Fallback Detection', hasFallbackDetection);
      
      // Test 5: Location change handling
      const hasLocationEffect = /useLocation/.test(hookContent);
      this.recordTest('Hook: Location Change Effect', hasLocationEffect);
      
      const hasMediaQueryListener = /addListener|addEventListener/.test(hookContent);
      this.recordTest('Hook: Media Query Listener', hasMediaQueryListener);
      
      return true;
      
    } catch (error) {
      this.recordTest('Hook: File Access', false, { error: error.message });
      return false;
    }
  }

  validateTestFilesExist() {
    this.log('info', 'Validating test files exist and contain required tests');
    
    const testFiles = [
      {
        name: 'PWA Functionality Unit Tests',
        path: path.join(__dirname, 'pwa-offline-functionality.test.js'),
        requiredContent: [
          'Browser vs PWA Mode Offline Behavior',
          'Service Worker PWA Mode Detection',
          'PWA Mode Detection Accuracy',
          'Service Worker Communication'
        ]
      },
      {
        name: 'Service Worker Unit Tests',
        path: path.join(__dirname, 'service-worker-pwa-mode.test.js'),
        requiredContent: [
          'PWA Mode Status Reception',
          'Navigation Request Handling',
          'PWA Mode Detection Function',
          'Service Worker Message Handling'
        ]
      },
      {
        name: 'Integration Test Page',
        path: path.join(__dirname, '..', '..', 'public', 'pwa-offline-integration-test.html'),
        requiredContent: [
          'PWA Mode Detection Accuracy',
          'Service Worker Communication',
          'Browser Mode Offline Behavior',
          'PWA Mode Offline Content Serving'
        ]
      }
    ];
    
    for (const testFile of testFiles) {
      try {
        const content = fs.readFileSync(testFile.path, 'utf8');
        
        let allContentFound = true;
        const missingContent = [];
        
        for (const requiredText of testFile.requiredContent) {
          if (!content.includes(requiredText)) {
            allContentFound = false;
            missingContent.push(requiredText);
          }
        }
        
        this.recordTest(`Test File: ${testFile.name}`, allContentFound, {
          path: testFile.path,
          missingContent: missingContent.length > 0 ? missingContent : 'None'
        });
        
      } catch (error) {
        this.recordTest(`Test File: ${testFile.name}`, false, { 
          path: testFile.path,
          error: error.message 
        });
      }
    }
  }

  validateRequirementsCoverage() {
    this.log('info', 'Validating requirements coverage in implementation');
    
    // Check if implementation covers all requirements
    const requirements = {
      '1.1': {
        description: 'Browser mode shows offline errors for driver-connect',
        implementationChecks: [
          'SW: Driver Connect Route Check',
          'SW: PWA Mode Routing Logic',
          'SW: Navigation Handler Function'
        ]
      },
      '1.2': {
        description: 'PWA mode serves cached content for driver-connect',
        implementationChecks: [
          'SW: PWA Mode Routing Logic',
          'SW: Navigation Handler Function',
          'SW: Driver Connect Route Check'
        ]
      },
      '1.3': {
        description: 'Service worker detects PWA mode correctly',
        implementationChecks: [
          'SW: PWA Mode Detection Function',
          'SW: PWA Mode Status Message Handler',
          'SW: Client Communication Logic'
        ]
      },
      '1.4': {
        description: 'Service worker uses PWA mode for routing decisions',
        implementationChecks: [
          'SW: PWA Mode Routing Logic',
          'SW: Navigation Handler Function',
          'SW: PWA Mode Detection Function'
        ]
      },
      '5.1': {
        description: 'PWA mode detection accuracy',
        implementationChecks: [
          'Hook: Display Mode Detection',
          'Hook: iOS Standalone Detection',
          'Hook: Android App Detection',
          'Hook: Multiple Detection Methods'
        ]
      },
      '5.2': {
        description: 'PWA mode communication to service worker',
        implementationChecks: [
          'Hook: Service Worker Communication',
          'Hook: PWA Mode Status Message',
          'SW: PWA Mode Status Message Handler'
        ]
      },
      '5.3': {
        description: 'Real-time PWA mode detection',
        implementationChecks: [
          'Hook: Detect and Send PWA Mode Function',
          'Hook: Location Change Effect',
          'Hook: Media Query Listener'
        ]
      },
      '5.4': {
        description: 'Service worker PWA mode requests',
        implementationChecks: [
          'SW: PWA Mode Request Handler',
          'SW: Message Event Listener',
          'Hook: Service Worker Communication'
        ]
      }
    };
    
    for (const [reqId, requirement] of Object.entries(requirements)) {
      const implementationComplete = requirement.implementationChecks.every(checkName => {
        return this.results.details.some(result => 
          result.test === checkName && result.passed
        );
      });
      
      this.recordTest(`Requirement ${reqId}`, implementationComplete, {
        description: requirement.description,
        checks: requirement.implementationChecks,
        status: implementationComplete ? 'All checks passed' : 'Some checks failed'
      });
    }
  }

  generateValidationReport() {
    this.log('info', 'Generating validation report');
    
    const report = {
      summary: {
        totalTests: this.results.total,
        passed: this.results.passed,
        failed: this.results.failed,
        passRate: this.results.total > 0 ? (this.results.passed / this.results.total * 100).toFixed(2) + '%' : '0%'
      },
      timestamp: new Date().toISOString(),
      testDetails: this.results.details,
      recommendations: []
    };
    
    // Add recommendations based on failures
    const failedTests = this.results.details.filter(test => !test.passed);
    
    if (failedTests.length === 0) {
      report.recommendations.push('✅ All implementation checks passed! PWA offline functionality is properly implemented.');
      report.recommendations.push('✅ Service worker has all required PWA mode detection features.');
      report.recommendations.push('✅ PWA status hook has all required detection methods.');
      report.recommendations.push('✅ All test files are present and contain required test cases.');
      report.recommendations.push('✅ All requirements are covered by the implementation.');
    } else {
      if (failedTests.some(test => test.test.includes('SW:'))) {
        report.recommendations.push('❌ Service worker implementation is missing some PWA mode features.');
        report.recommendations.push('🔧 Check service worker file for PWA mode detection and routing logic.');
      }
      
      if (failedTests.some(test => test.test.includes('Hook:'))) {
        report.recommendations.push('❌ PWA status hook is missing some detection methods.');
        report.recommendations.push('🔧 Check usePWAStatus hook for multiple PWA mode detection methods.');
      }
      
      if (failedTests.some(test => test.test.includes('Test File:'))) {
        report.recommendations.push('❌ Some test files are missing or incomplete.');
        report.recommendations.push('🔧 Ensure all test files exist and contain required test cases.');
      }
      
      if (failedTests.some(test => test.test.includes('Requirement'))) {
        report.recommendations.push('❌ Some requirements are not fully covered by implementation.');
        report.recommendations.push('🔧 Review failed requirement checks and implement missing features.');
      }
    }
    
    // Save report to file
    const reportPath = path.join(__dirname, 'pwa-implementation-validation-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    this.log('info', `Validation report saved to: ${reportPath}`);
    
    return report;
  }

  async runValidation() {
    this.log('info', 'Starting PWA offline functionality implementation validation');
    
    try {
      // Run all validation checks
      this.validateServiceWorkerImplementation();
      this.validatePWAStatusHookImplementation();
      this.validateTestFilesExist();
      this.validateRequirementsCoverage();
      
      // Generate final report
      const report = this.generateValidationReport();
      
      // Print summary
      console.log('\n' + '='.repeat(70));
      console.log('PWA OFFLINE FUNCTIONALITY IMPLEMENTATION VALIDATION');
      console.log('='.repeat(70));
      console.log(`Total Checks: ${report.summary.totalTests}`);
      console.log(`Passed: ${report.summary.passed}`);
      console.log(`Failed: ${report.summary.failed}`);
      console.log(`Pass Rate: ${report.summary.passRate}`);
      console.log('='.repeat(70));
      
      if (report.summary.failed > 0) {
        console.log('\n❌ FAILED CHECKS:');
        const failedTests = this.results.details.filter(test => !test.passed);
        failedTests.forEach(test => {
          console.log(`  - ${test.test}`);
          if (test.details) {
            console.log(`    Details: ${JSON.stringify(test.details)}`);
          }
        });
      }
      
      console.log('\n📋 RECOMMENDATIONS:');
      report.recommendations.forEach(rec => {
        console.log(`  ${rec}`);
      });
      
      console.log(`\n📄 Detailed report: ${path.join(__dirname, 'pwa-implementation-validation-report.json')}`);
      
      return report;
      
    } catch (error) {
      this.log('error', 'Validation failed', error);
      throw error;
    }
  }
}

// Run validation if this file is executed directly
if (require.main === module) {
  const validator = new PWAImplementationValidator();
  
  validator.runValidation()
    .then(report => {
      process.exit(report.summary.failed > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error('Validation failed:', error);
      process.exit(1);
    });
}

module.exports = PWAImplementationValidator;