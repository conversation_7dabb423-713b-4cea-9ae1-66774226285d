<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Offline Integration Test</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            padding: 20px; 
            background-color: #f8f9fa;
            line-height: 1.6;
        }
        .test-section { 
            margin: 20px 0; 
            padding: 20px; 
            border: 1px solid #dee2e6; 
            border-radius: 8px;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section h2 {
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        .success { color: #28a745; font-weight: 500; }
        .error { color: #dc3545; font-weight: 500; }
        .info { color: #17a2b8; font-weight: 500; }
        .warning { color: #ffc107; font-weight: 500; }
        
        button { 
            padding: 12px 20px; 
            margin: 8px 5px; 
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 140px;
        }
        
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: #212529; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-info { background-color: #17a2b8; color: white; }
        
        .btn-primary:hover { background-color: #0056b3; }
        .btn-success:hover { background-color: #1e7e34; }
        .btn-warning:hover { background-color: #e0a800; }
        .btn-danger:hover { background-color: #c82333; }
        .btn-info:hover { background-color: #138496; }
        
        .test-result {
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            border-left: 4px solid;
        }
        .test-result.pass {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .test-result.fail {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .test-result.pending {
            background-color: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        
        pre { 
            background: #f8f9fa; 
            padding: 15px; 
            overflow-x: auto; 
            border-radius: 6px;
            border: 1px solid #e9ecef;
            font-size: 13px;
            line-height: 1.4;
        }
        
        .instructions-panel {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .instructions-panel h3 {
            margin-top: 0;
            color: white;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            vertical-align: middle;
        }
        .status-pass { background-color: #28a745; }
        .status-fail { background-color: #dc3545; }
        .status-pending { background-color: #ffc107; }
        .status-unknown { background-color: #6c757d; }
    </style>
</head>
<body>
    <h1>PWA Offline Integration Test Suite</h1>
    
    <div class="instructions-panel">
        <h3>🧪 PWA-Only Offline Functionality Integration Tests</h3>
        <p>This test suite validates the complete PWA-only offline functionality by running real browser tests.</p>
        
        <div style="margin: 15px 0;">
            <h4>Test Coverage:</h4>
            <ul>
                <li><strong>Requirement 1.1:</strong> Browser mode shows offline errors for driver-connect</li>
                <li><strong>Requirement 1.2:</strong> PWA mode serves cached content for driver-connect</li>
                <li><strong>Requirement 1.3:</strong> Service worker correctly detects PWA mode</li>
                <li><strong>Requirement 1.4:</strong> Service worker uses PWA mode for routing decisions</li>
                <li><strong>Requirements 5.1-5.4:</strong> PWA mode detection accuracy and communication</li>
            </ul>
        </div>
        
        <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 5px;">
            <strong>⚠️ Test Instructions:</strong>
            <ol style="margin: 5px 0; padding-left: 20px;">
                <li>Run tests in both browser tab and PWA mode</li>
                <li>Test with network online and offline</li>
                <li>Check browser console for detailed logs</li>
                <li>Verify service worker communication</li>
            </ol>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Test Environment Status</h2>
        <div id="environment-status"></div>
        <button class="btn-info" onclick="checkEnvironment()">Check Environment</button>
    </div>
    
    <div class="test-section">
        <h2>Test 1: PWA Mode Detection Accuracy</h2>
        <div id="test1-results"></div>
        <button class="btn-primary" onclick="runTest1()">Run PWA Mode Detection Test</button>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Service Worker Communication</h2>
        <div id="test2-results"></div>
        <button class="btn-primary" onclick="runTest2()">Run Service Worker Communication Test</button>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Browser Mode Offline Behavior</h2>
        <div id="test3-results"></div>
        <button class="btn-warning" onclick="runTest3()">Run Browser Mode Offline Test</button>
    </div>
    
    <div class="test-section">
        <h2>Test 4: PWA Mode Offline Content Serving</h2>
        <div id="test4-results"></div>
        <button class="btn-success" onclick="runTest4()">Run PWA Mode Offline Test</button>
    </div>
    
    <div class="test-section">
        <h2>Test 5: Navigation Request Handling</h2>
        <div id="test5-results"></div>
        <button class="btn-primary" onclick="runTest5()">Run Navigation Test</button>
    </div>
    
    <div class="test-section">
        <h2>Test 6: PWA Mode Consistency</h2>
        <div id="test6-results"></div>
        <button class="btn-info" onclick="runTest6()">Run PWA Mode Consistency Test</button>
    </div>
    
    <div class="test-section">
        <h2>Run All Tests</h2>
        <div id="all-tests-results"></div>
        <button class="btn-success" onclick="runAllTests()">Run Complete Test Suite</button>
        <button class="btn-warning" onclick="clearAllResults()">Clear Results</button>
    </div>

    <script>
        // Test state management
        let testResults = {};
        let serviceWorkerReady = false;
        let currentPWAMode = false;
        
        // Enhanced logging
        function log(level, message, data = null) {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
            
            console[level](logMessage, data || '');
            
            // Also log to test results if needed
            if (level === 'error') {
                updateTestResult('error', `${logMessage}${data ? ': ' + JSON.stringify(data) : ''}`);
            }
        }
        
        // Test result management
        function updateTestResult(testId, result, status = 'info') {
            testResults[testId] = { result, status, timestamp: new Date().toISOString() };
            
            const resultElement = document.getElementById(`${testId}-results`);
            if (resultElement) {
                const statusClass = status === 'pass' ? 'pass' : status === 'fail' ? 'fail' : 'pending';
                const statusIcon = status === 'pass' ? 'status-pass' : status === 'fail' ? 'status-fail' : 'status-pending';
                
                resultElement.innerHTML = `
                    <div class="test-result ${statusClass}">
                        <span class="status-indicator ${statusIcon}"></span>
                        <strong>${status.toUpperCase()}:</strong> ${result}
                    </div>
                `;
            }
        }
        
        function clearAllResults() {
            testResults = {};
            const resultElements = document.querySelectorAll('[id$="-results"]');
            resultElements.forEach(element => {
                element.innerHTML = '';
            });
            log('info', 'All test results cleared');
        }
        
        // Environment check
        async function checkEnvironment() {
            log('info', 'Checking test environment...');
            
            const environment = {
                userAgent: navigator.userAgent,
                online: navigator.onLine,
                url: window.location.href,
                pathname: window.location.pathname,
                serviceWorkerSupported: 'serviceWorker' in navigator,
                cacheSupported: 'caches' in window,
                indexedDBSupported: 'indexedDB' in window,
                pwaMode: detectPWAMode(),
                displayMode: getDisplayMode(),
                standalone: navigator.standalone
            };
            
            // Check service worker status
            if ('serviceWorker' in navigator) {
                try {
                    const registration = await navigator.serviceWorker.getRegistration();
                    environment.serviceWorkerRegistered = !!registration;
                    environment.serviceWorkerActive = !!(registration && registration.active);
                    serviceWorkerReady = environment.serviceWorkerActive;
                } catch (error) {
                    environment.serviceWorkerError = error.message;
                    log('error', 'Service worker check failed', error);
                }
            }
            
            currentPWAMode = environment.pwaMode;
            
            document.getElementById('environment-status').innerHTML = `
                <pre>${JSON.stringify(environment, null, 2)}</pre>
            `;
            
            log('info', 'Environment check completed', environment);
            return environment;
        }
        
        // PWA mode detection (same logic as the hook)
        function detectPWAMode() {
            try {
                const standaloneMatch = window.matchMedia('(display-mode: standalone)').matches;
                const iOSStandalone = window.navigator.standalone === true;
                const androidApp = document.referrer.includes('android-app://');
                
                return standaloneMatch || iOSStandalone || androidApp;
            } catch (error) {
                log('error', 'PWA mode detection failed', error);
                return false;
            }
        }
        
        function getDisplayMode() {
            if (window.matchMedia('(display-mode: standalone)').matches) return 'standalone';
            if (window.matchMedia('(display-mode: minimal-ui)').matches) return 'minimal-ui';
            if (window.matchMedia('(display-mode: fullscreen)').matches) return 'fullscreen';
            return 'browser';
        }
        
        // Test 1: PWA Mode Detection Accuracy
        async function runTest1() {
            log('info', 'Running Test 1: PWA Mode Detection Accuracy');
            updateTestResult('test1', 'Running PWA mode detection tests...', 'pending');
            
            try {
                const detectionMethods = [];
                
                // Test display-mode media query
                const standaloneMatch = window.matchMedia('(display-mode: standalone)').matches;
                detectionMethods.push({ method: 'display-mode standalone', result: standaloneMatch });
                
                // Test iOS standalone
                const iOSStandalone = window.navigator.standalone === true;
                detectionMethods.push({ method: 'iOS navigator.standalone', result: iOSStandalone });
                
                // Test Android app referrer
                const androidApp = document.referrer.includes('android-app://');
                detectionMethods.push({ method: 'Android app referrer', result: androidApp });
                
                // Test URL parameters
                const urlParams = new URLSearchParams(window.location.search);
                const pwaParam = urlParams.has('pwa') || urlParams.has('standalone');
                detectionMethods.push({ method: 'URL parameters', result: pwaParam });
                
                const finalPWAMode = standaloneMatch || iOSStandalone || androidApp || pwaParam;
                
                const testResult = {
                    detectedPWAMode: finalPWAMode,
                    detectionMethods,
                    displayMode: getDisplayMode(),
                    userAgent: navigator.userAgent.substring(0, 100) + '...'
                };
                
                // Verify consistency with current detection
                const consistent = finalPWAMode === currentPWAMode;
                
                if (consistent) {
                    updateTestResult('test1', `PWA mode detection consistent: ${finalPWAMode}. Methods: ${JSON.stringify(detectionMethods, null, 2)}`, 'pass');
                } else {
                    updateTestResult('test1', `PWA mode detection inconsistent. Expected: ${currentPWAMode}, Got: ${finalPWAMode}`, 'fail');
                }
                
                log('info', 'Test 1 completed', testResult);
                return testResult;
                
            } catch (error) {
                log('error', 'Test 1 failed', error);
                updateTestResult('test1', `Test failed: ${error.message}`, 'fail');
                throw error;
            }
        }
        
        // Test 2: Service Worker Communication
        async function runTest2() {
            log('info', 'Running Test 2: Service Worker Communication');
            updateTestResult('test2', 'Testing service worker communication...', 'pending');
            
            try {
                if (!('serviceWorker' in navigator)) {
                    updateTestResult('test2', 'Service worker not supported in this browser', 'fail');
                    return;
                }
                
                const registration = await navigator.serviceWorker.getRegistration();
                if (!registration || !registration.active) {
                    updateTestResult('test2', 'Service worker not registered or active', 'fail');
                    return;
                }
                
                // Test PWA mode status communication
                const testResults = [];
                
                // Set up message listener
                const messagePromise = new Promise((resolve) => {
                    const messageHandler = (event) => {
                        if (event.data && event.data.type === 'PWA_MODE_CONFIRMATION') {
                            navigator.serviceWorker.removeEventListener('message', messageHandler);
                            resolve(event.data);
                        }
                    };
                    navigator.serviceWorker.addEventListener('message', messageHandler);
                    
                    // Timeout after 5 seconds
                    setTimeout(() => {
                        navigator.serviceWorker.removeEventListener('message', messageHandler);
                        resolve(null);
                    }, 5000);
                });
                
                // Send PWA mode status to service worker
                const pwaMode = detectPWAMode();
                const message = {
                    type: 'PWA_MODE_STATUS',
                    isPWA: pwaMode,
                    currentPath: window.location.pathname,
                    timestamp: new Date().toISOString(),
                    source: 'integration-test'
                };
                
                registration.active.postMessage(message);
                log('info', 'Sent PWA mode status to service worker', message);
                
                // Wait for confirmation
                const confirmation = await messagePromise;
                
                if (confirmation) {
                    testResults.push({
                        test: 'PWA mode status communication',
                        passed: confirmation.confirmedMode === pwaMode,
                        expected: pwaMode,
                        received: confirmation.confirmedMode
                    });
                } else {
                    testResults.push({
                        test: 'PWA mode status communication',
                        passed: false,
                        error: 'No confirmation received from service worker'
                    });
                }
                
                // Test client ready notification
                const readyMessage = {
                    type: 'CLIENT_READY',
                    timestamp: new Date().toISOString(),
                    source: 'integration-test'
                };
                
                registration.active.postMessage(readyMessage);
                testResults.push({
                    test: 'Client ready notification',
                    passed: true,
                    note: 'Message sent successfully'
                });
                
                const allPassed = testResults.every(result => result.passed);
                
                if (allPassed) {
                    updateTestResult('test2', `Service worker communication successful. Results: ${JSON.stringify(testResults, null, 2)}`, 'pass');
                } else {
                    updateTestResult('test2', `Service worker communication issues. Results: ${JSON.stringify(testResults, null, 2)}`, 'fail');
                }
                
                log('info', 'Test 2 completed', testResults);
                return testResults;
                
            } catch (error) {
                log('error', 'Test 2 failed', error);
                updateTestResult('test2', `Test failed: ${error.message}`, 'fail');
                throw error;
            }
        }
        
        // Test 3: Browser Mode Offline Behavior
        async function runTest3() {
            log('info', 'Running Test 3: Browser Mode Offline Behavior');
            updateTestResult('test3', 'Testing browser mode offline behavior...', 'pending');
            
            try {
                const currentMode = detectPWAMode();
                
                if (currentMode) {
                    updateTestResult('test3', 'Cannot test browser mode behavior - currently in PWA mode. Please run this test in a regular browser tab.', 'pending');
                    return;
                }
                
                // Test navigation to driver-connect route
                const testUrl = '/driver-connect';
                
                // Create a test iframe to simulate navigation
                const iframe = document.createElement('iframe');
                iframe.style.display = 'none';
                document.body.appendChild(iframe);
                
                const navigationPromise = new Promise((resolve) => {
                    iframe.onload = () => {
                        try {
                            // Check if the iframe loaded successfully or failed
                            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                            resolve({
                                loaded: true,
                                url: iframe.contentWindow.location.href,
                                title: iframeDoc.title
                            });
                        } catch (error) {
                            resolve({
                                loaded: false,
                                error: error.message
                            });
                        }
                    };
                    
                    iframe.onerror = (error) => {
                        resolve({
                            loaded: false,
                            error: 'Navigation failed'
                        });
                    };
                    
                    // Timeout after 10 seconds
                    setTimeout(() => {
                        resolve({
                            loaded: false,
                            error: 'Navigation timeout'
                        });
                    }, 10000);
                });
                
                // Simulate offline condition by trying to navigate
                iframe.src = testUrl;
                
                const result = await navigationPromise;
                document.body.removeChild(iframe);
                
                // In browser mode offline, we expect navigation to fail
                if (!result.loaded || result.error) {
                    updateTestResult('test3', `Browser mode offline behavior correct - navigation failed as expected: ${result.error || 'Navigation failed'}`, 'pass');
                } else {
                    updateTestResult('test3', `Browser mode offline behavior unexpected - navigation succeeded when it should fail in offline mode`, 'fail');
                }
                
                log('info', 'Test 3 completed', result);
                return result;
                
            } catch (error) {
                log('error', 'Test 3 failed', error);
                updateTestResult('test3', `Test failed: ${error.message}`, 'fail');
                throw error;
            }
        }
        
        // Test 4: PWA Mode Offline Content Serving
        async function runTest4() {
            log('info', 'Running Test 4: PWA Mode Offline Content Serving');
            updateTestResult('test4', 'Testing PWA mode offline content serving...', 'pending');
            
            try {
                const currentMode = detectPWAMode();
                
                if (!currentMode) {
                    updateTestResult('test4', 'Cannot test PWA mode behavior - currently in browser mode. Please run this test in PWA mode.', 'pending');
                    return;
                }
                
                // Test cache availability
                if (!('caches' in window)) {
                    updateTestResult('test4', 'Cache API not supported', 'fail');
                    return;
                }
                
                const cacheNames = await caches.keys();
                log('info', 'Available caches', cacheNames);
                
                // Check if main app is cached
                const mainCacheAvailable = await caches.match('/') || await caches.match('/index.html');
                
                if (mainCacheAvailable) {
                    updateTestResult('test4', `PWA mode offline content available - main app cached. Available caches: ${cacheNames.join(', ')}`, 'pass');
                } else {
                    updateTestResult('test4', `PWA mode offline content not available - main app not cached. Available caches: ${cacheNames.join(', ')}`, 'fail');
                }
                
                // Test driver-connect specific caching
                const driverConnectCached = await caches.match('/driver-connect');
                
                const result = {
                    isPWAMode: currentMode,
                    cacheNames,
                    mainAppCached: !!mainCacheAvailable,
                    driverConnectCached: !!driverConnectCached
                };
                
                log('info', 'Test 4 completed', result);
                return result;
                
            } catch (error) {
                log('error', 'Test 4 failed', error);
                updateTestResult('test4', `Test failed: ${error.message}`, 'fail');
                throw error;
            }
        }
        
        // Test 5: Navigation Request Handling
        async function runTest5() {
            log('info', 'Running Test 5: Navigation Request Handling');
            updateTestResult('test5', 'Testing navigation request handling...', 'pending');
            
            try {
                const currentMode = detectPWAMode();
                const testResults = [];
                
                // Test different route types
                const routes = [
                    { path: '/driver-connect', shouldWorkOffline: currentMode },
                    { path: '/trip-scanner', shouldWorkOffline: true }, // Should work in both modes
                    { path: '/dashboard', shouldWorkOffline: true }
                ];
                
                for (const route of routes) {
                    try {
                        // Use fetch to test route availability
                        const response = await fetch(route.path, { 
                            method: 'HEAD',
                            cache: 'no-cache'
                        });
                        
                        testResults.push({
                            route: route.path,
                            available: response.ok,
                            status: response.status,
                            expected: route.shouldWorkOffline,
                            passed: response.ok === route.shouldWorkOffline
                        });
                        
                    } catch (error) {
                        // Network error - check if this is expected
                        const expectedFailure = !route.shouldWorkOffline;
                        testResults.push({
                            route: route.path,
                            available: false,
                            error: error.message,
                            expected: route.shouldWorkOffline,
                            passed: expectedFailure
                        });
                    }
                }
                
                const allPassed = testResults.every(result => result.passed);
                
                if (allPassed) {
                    updateTestResult('test5', `Navigation handling correct for ${currentMode ? 'PWA' : 'browser'} mode. Results: ${JSON.stringify(testResults, null, 2)}`, 'pass');
                } else {
                    updateTestResult('test5', `Navigation handling issues detected. Results: ${JSON.stringify(testResults, null, 2)}`, 'fail');
                }
                
                log('info', 'Test 5 completed', testResults);
                return testResults;
                
            } catch (error) {
                log('error', 'Test 5 failed', error);
                updateTestResult('test5', `Test failed: ${error.message}`, 'fail');
                throw error;
            }
        }
        
        // Test 6: PWA Mode Consistency
        async function runTest6() {
            log('info', 'Running Test 6: PWA Mode Consistency');
            updateTestResult('test6', 'Testing PWA mode consistency...', 'pending');
            
            try {
                const detections = [];
                
                // Multiple detection attempts
                for (let i = 0; i < 5; i++) {
                    const detection = detectPWAMode();
                    detections.push(detection);
                    await new Promise(resolve => setTimeout(resolve, 100)); // Small delay
                }
                
                // Check consistency
                const allSame = detections.every(detection => detection === detections[0]);
                const firstDetection = detections[0];
                
                // Test media query listener
                const mediaQuery = window.matchMedia('(display-mode: standalone)');
                const mediaQueryResult = mediaQuery.matches;
                
                // Test with service worker communication
                let serviceWorkerConsistent = true;
                if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
                    try {
                        navigator.serviceWorker.controller.postMessage({
                            type: 'PWA_MODE_STATUS',
                            isPWA: firstDetection,
                            currentPath: window.location.pathname,
                            timestamp: new Date().toISOString(),
                            source: 'consistency-test'
                        });
                    } catch (error) {
                        serviceWorkerConsistent = false;
                        log('warn', 'Service worker communication failed during consistency test', error);
                    }
                }
                
                const result = {
                    detections,
                    consistent: allSame,
                    detectedMode: firstDetection,
                    mediaQueryMatches: mediaQueryResult,
                    serviceWorkerCommunication: serviceWorkerConsistent
                };
                
                if (allSame && serviceWorkerConsistent) {
                    updateTestResult('test6', `PWA mode detection consistent across ${detections.length} attempts: ${firstDetection}. Media query: ${mediaQueryResult}`, 'pass');
                } else {
                    updateTestResult('test6', `PWA mode detection inconsistent. Results: ${JSON.stringify(result, null, 2)}`, 'fail');
                }
                
                log('info', 'Test 6 completed', result);
                return result;
                
            } catch (error) {
                log('error', 'Test 6 failed', error);
                updateTestResult('test6', `Test failed: ${error.message}`, 'fail');
                throw error;
            }
        }
        
        // Run all tests
        async function runAllTests() {
            log('info', 'Running complete PWA offline functionality test suite');
            updateTestResult('all-tests', 'Running complete test suite...', 'pending');
            
            try {
                const environment = await checkEnvironment();
                
                const testResults = {
                    environment,
                    tests: {}
                };
                
                // Run all tests in sequence
                testResults.tests.test1 = await runTest1();
                testResults.tests.test2 = await runTest2();
                testResults.tests.test3 = await runTest3();
                testResults.tests.test4 = await runTest4();
                testResults.tests.test5 = await runTest5();
                testResults.tests.test6 = await runTest6();
                
                // Analyze overall results
                const testStatuses = Object.keys(testResults.tests).map(testId => {
                    const result = testResults[testId];
                    return result && result.status ? result.status : 'unknown';
                });
                
                const passedTests = Object.keys(testResults).filter(testId => 
                    testResults[testId] && testResults[testId].status === 'pass'
                ).length;
                
                const totalTests = Object.keys(testResults.tests).length;
                
                if (passedTests === totalTests) {
                    updateTestResult('all-tests', `All ${totalTests} tests passed! PWA offline functionality is working correctly.`, 'pass');
                } else {
                    updateTestResult('all-tests', `${passedTests}/${totalTests} tests passed. Some issues detected - check individual test results.`, 'fail');
                }
                
                log('info', 'Complete test suite finished', testResults);
                return testResults;
                
            } catch (error) {
                log('error', 'Test suite failed', error);
                updateTestResult('all-tests', `Test suite failed: ${error.message}`, 'fail');
                throw error;
            }
        }
        
        // Initialize on page load
        window.addEventListener('load', () => {
            log('info', 'PWA Offline Integration Test Suite loaded');
            checkEnvironment();
        });
    </script>
</body>
</html>