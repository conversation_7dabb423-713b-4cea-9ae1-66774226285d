// Clear development cache script
// Run this in browser console to clear cached hot-update files

async function clearDevCache() {
  console.log('Clearing development cache...');
  
  try {
    // Get all cache names
    const cacheNames = await caches.keys();
    console.log('Found caches:', cacheNames);
    
    // Clear all caches to remove hot-update files
    for (const cacheName of cacheNames) {
      const cache = await caches.open(cacheName);
      const requests = await cache.keys();
      
      // Remove hot-update files specifically
      for (const request of requests) {
        if (request.url.includes('.hot-update.') || 
            request.url.includes('webpack') ||
            request.url.includes('sockjs-node')) {
          await cache.delete(request);
          console.log('Deleted from cache:', request.url);
        }
      }
    }
    
    // Unregister and re-register service worker
    const registration = await navigator.serviceWorker.getRegistration();
    if (registration) {
      await registration.unregister();
      console.log('Service worker unregistered');
      
      // Wait a moment then reload
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    }
    
    console.log('Cache cleared successfully');
  } catch (error) {
    console.error('Error clearing cache:', error);
  }
}

// Run the function
clearDevCache();