<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Sync Recovery Tool</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            padding: 20px; 
            background-color: #f8f9fa;
            line-height: 1.6;
        }
        .tool-section { 
            margin: 20px 0; 
            padding: 20px; 
            border: 1px solid #dee2e6; 
            border-radius: 8px;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .tool-section h2 {
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        .success { color: #28a745; font-weight: 500; }
        .error { color: #dc3545; font-weight: 500; }
        .info { color: #17a2b8; font-weight: 500; }
        .warning { color: #ffc107; font-weight: 500; }
        
        button { 
            padding: 12px 20px; 
            margin: 8px 5px; 
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 140px;
        }
        
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: #212529; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-secondary { background-color: #6c757d; color: white; }
        
        button:hover { transform: translateY(-1px); opacity: 0.9; }
        button:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }
        
        pre { 
            background: #f8f9fa; 
            padding: 15px; 
            overflow-x: auto; 
            border-radius: 6px;
            border: 1px solid #e9ecef;
            font-size: 13px;
            line-height: 1.4;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            vertical-align: middle;
        }
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        .status-pending { background-color: #ffc107; }
        .status-unknown { background-color: #6c757d; }
        
        .alert {
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            border-left: 4px solid;
        }
        .alert-success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .alert-error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .alert-warning {
            background-color: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .alert-info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        
        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        
        @media (max-width: 768px) {
            body { padding: 10px; }
            .button-group { flex-direction: column; }
            button { width: 100%; margin: 5px 0; }
        }
    </style>
</head>
<body>
    <h1>🔧 PWA Sync Recovery Tool</h1>
    
    <div class="alert alert-info">
        <strong>🛠️ Sync Recovery Tool</strong><br>
        This tool helps diagnose and fix PWA sync errors. Use it when you see "Sync Error" in the driver-connect PWA or when debug buttons aren't working.
    </div>
    
    <div class="tool-section">
        <h2>1. System Status Check</h2>
        <div id="system-status"></div>
        <div class="button-group">
            <button class="btn-primary" onclick="checkSystemStatus()">
                <span class="status-indicator status-unknown"></span>
                Check System Status
            </button>
        </div>
    </div>
    
    <div class="tool-section">
        <h2>2. Sync Error Diagnosis</h2>
        <div id="sync-diagnosis"></div>
        <div class="button-group">
            <button class="btn-warning" onclick="diagnoseSyncErrors()">
                <span class="status-indicator status-pending"></span>
                Diagnose Sync Errors
            </button>
            <button class="btn-info" onclick="inspectOfflineData()">
                <span class="status-indicator status-unknown"></span>
                Inspect Offline Data
            </button>
        </div>
    </div>
    
    <div class="tool-section">
        <h2>3. Recovery Actions</h2>
        <div id="recovery-actions"></div>
        <div class="button-group">
            <button class="btn-success" onclick="recoverFromSyncError()">
                <span class="status-indicator status-online"></span>
                Auto-Recover Sync
            </button>
            <button class="btn-warning" onclick="clearCorruptedData()">
                <span class="status-indicator status-pending"></span>
                Clear Corrupted Data
            </button>
            <button class="btn-danger" onclick="resetAllOfflineData()">
                <span class="status-indicator status-offline"></span>
                Reset All Offline Data
            </button>
        </div>
    </div>
    
    <div class="tool-section">
        <h2>4. Manual Sync Test</h2>
        <div id="manual-sync-test"></div>
        <div class="button-group">
            <button class="btn-primary" onclick="testManualSync()">
                <span class="status-indicator status-unknown"></span>
                Test Manual Sync
            </button>
            <button class="btn-secondary" onclick="simulateOfflineData()">
                <span class="status-indicator status-pending"></span>
                Simulate Test Data
            </button>
        </div>
    </div>
    
    <div class="tool-section">
        <h2>5. Debug Logs</h2>
        <div id="debug-logs"></div>
        <div class="button-group">
            <button class="btn-secondary" onclick="exportLogs()">
                <span class="status-indicator status-unknown"></span>
                Export Debug Logs
            </button>
            <button class="btn-warning" onclick="clearLogs()">
                <span class="status-indicator status-pending"></span>
                Clear Logs
            </button>
        </div>
    </div>

    <script>
        let logs = [];
        
        // Enhanced logging system
        function log(level, message, data = null) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = {
                timestamp,
                level,
                message,
                data
            };
            
            console[level] ? console[level](message, data) : console.log(message, data);
            
            const displayMessage = `${timestamp} [${level.toUpperCase()}] ${message}`;
            logs.push(displayMessage);
            if (data) {
                logs.push(`${timestamp} [${level.toUpperCase()}] Data: ${JSON.stringify(data, null, 2)}`);
            }
            
            updateDebugLogs();
        }
        
        function updateDebugLogs() {
            const element = document.getElementById('debug-logs');
            if (element) {
                element.innerHTML = '<pre>' + logs.slice(-20).join('\n') + '</pre>';
            }
        }
        
        function clearLogs() {
            logs = [];
            updateDebugLogs();
            log('info', 'Debug logs cleared');
        }
        
        // System Status Check
        async function checkSystemStatus() {
            const statusElement = document.getElementById('system-status');
            statusElement.innerHTML = '<div class="info">🔍 Checking system status...</div>';
            
            try {
                const status = {
                    online: navigator.onLine,
                    pwaMode: window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone === true,
                    serviceWorkerSupported: 'serviceWorker' in navigator,
                    indexedDBSupported: 'indexedDB' in window,
                    serviceWorkerActive: false,
                    indexedDBAccessible: false
                };
                
                // Check service worker
                if (status.serviceWorkerSupported) {
                    try {
                        const registration = await navigator.serviceWorker.getRegistration();
                        status.serviceWorkerActive = !!registration?.active;
                    } catch (error) {
                        log('error', 'Service worker check failed', error);
                    }
                }
                
                // Check IndexedDB
                if (status.indexedDBSupported) {
                    try {
                        const request = indexedDB.open('test-db', 1);
                        await new Promise((resolve, reject) => {
                            request.onsuccess = () => {
                                request.result.close();
                                resolve();
                            };
                            request.onerror = reject;
                        });
                        status.indexedDBAccessible = true;
                    } catch (error) {
                        log('error', 'IndexedDB check failed', error);
                    }
                }
                
                let html = '<div class="alert alert-info"><strong>System Status:</strong></div>';
                html += '<ul>';
                html += `<li>Network: ${status.online ? '<span class="success">Online</span>' : '<span class="error">Offline</span>'}</li>`;
                html += `<li>PWA Mode: ${status.pwaMode ? '<span class="success">Active</span>' : '<span class="warning">Browser Mode</span>'}</li>`;
                html += `<li>Service Worker: ${status.serviceWorkerActive ? '<span class="success">Active</span>' : '<span class="error">Inactive</span>'}</li>`;
                html += `<li>IndexedDB: ${status.indexedDBAccessible ? '<span class="success">Accessible</span>' : '<span class="error">Not Accessible</span>'}</li>`;
                html += '</ul>';
                
                statusElement.innerHTML = html;
                log('info', 'System status checked', status);
                
            } catch (error) {
                statusElement.innerHTML = '<div class="alert alert-error">❌ System status check failed: ' + error.message + '</div>';
                log('error', 'System status check failed', error);
            }
        }
        
        // Diagnose Sync Errors
        async function diagnoseSyncErrors() {
            const diagnosisElement = document.getElementById('sync-diagnosis');
            diagnosisElement.innerHTML = '<div class="info">🔍 Diagnosing sync errors...</div>';
            
            try {
                const diagnosis = {
                    offlineDataExists: false,
                    corruptedConnections: 0,
                    pendingConnections: 0,
                    failedConnections: 0,
                    errors: []
                };
                
                // Try to access offline database
                try {
                    const db = await openOfflineDB();
                    if (db) {
                        // Check for offline data
                        const connections = await getAllConnections(db);
                        diagnosis.offlineDataExists = connections.length > 0;
                        
                        // Analyze connections
                        for (const conn of connections) {
                            if (conn.status === 'pending') diagnosis.pendingConnections++;
                            if (conn.status === 'failed') diagnosis.failedConnections++;
                            
                            // Check for corruption
                            if (!conn.apiPayload && !conn.connectionData) {
                                diagnosis.corruptedConnections++;
                            }
                        }
                        
                        db.close();
                    }
                } catch (error) {
                    diagnosis.errors.push(`Database access error: ${error.message}`);
                }
                
                let html = '<div class="alert alert-info"><strong>Sync Diagnosis Results:</strong></div>';
                html += '<ul>';
                html += `<li>Offline Data: ${diagnosis.offlineDataExists ? '<span class="warning">Found</span>' : '<span class="success">None</span>'}</li>`;
                html += `<li>Pending Connections: <span class="${diagnosis.pendingConnections > 0 ? 'warning' : 'success'}">${diagnosis.pendingConnections}</span></li>`;
                html += `<li>Failed Connections: <span class="${diagnosis.failedConnections > 0 ? 'error' : 'success'}">${diagnosis.failedConnections}</span></li>`;
                html += `<li>Corrupted Data: <span class="${diagnosis.corruptedConnections > 0 ? 'error' : 'success'}">${diagnosis.corruptedConnections}</span></li>`;
                html += '</ul>';
                
                if (diagnosis.errors.length > 0) {
                    html += '<div class="alert alert-error"><strong>Errors Found:</strong><ul>';
                    diagnosis.errors.forEach(error => {
                        html += `<li>${error}</li>`;
                    });
                    html += '</ul></div>';
                }
                
                diagnosisElement.innerHTML = html;
                log('info', 'Sync diagnosis completed', diagnosis);
                
            } catch (error) {
                diagnosisElement.innerHTML = '<div class="alert alert-error">❌ Sync diagnosis failed: ' + error.message + '</div>';
                log('error', 'Sync diagnosis failed', error);
            }
        }
        
        // Recovery Functions
        async function recoverFromSyncError() {
            const recoveryElement = document.getElementById('recovery-actions');
            recoveryElement.innerHTML = '<div class="info">🔄 Attempting sync recovery...</div>';
            
            try {
                // This would call the actual recovery function from the PWA
                if (window.parent && window.parent.triggerSyncRecovery) {
                    const result = await window.parent.triggerSyncRecovery();
                    
                    if (result.success) {
                        recoveryElement.innerHTML = '<div class="alert alert-success">✅ Sync recovery successful: ' + result.message + '</div>';
                    } else {
                        recoveryElement.innerHTML = '<div class="alert alert-error">❌ Sync recovery failed: ' + result.message + '</div>';
                    }
                } else {
                    recoveryElement.innerHTML = '<div class="alert alert-warning">⚠️ Recovery function not available. Please use this tool from within the PWA.</div>';
                }
                
                log('info', 'Sync recovery attempted');
                
            } catch (error) {
                recoveryElement.innerHTML = '<div class="alert alert-error">❌ Recovery failed: ' + error.message + '</div>';
                log('error', 'Sync recovery failed', error);
            }
        }
        
        // Helper functions for IndexedDB access
        async function openOfflineDB() {
            return new Promise((resolve, reject) => {
                const request = indexedDB.open('HaulingQROfflineDB', 1);
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
                request.onupgradeneeded = () => reject(new Error('Database not initialized'));
            });
        }
        
        async function getAllConnections(db) {
            return new Promise((resolve, reject) => {
                if (!db.objectStoreNames.contains('connectionQueue')) {
                    resolve([]);
                    return;
                }
                
                const transaction = db.transaction(['connectionQueue'], 'readonly');
                const store = transaction.objectStore('connectionQueue');
                const request = store.getAll();
                
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });
        }
        
        // Initialize tool
        function initializeTool() {
            log('info', 'PWA Sync Recovery Tool initialized');
            checkSystemStatus();
        }
        
        // Run initialization
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeTool);
        } else {
            initializeTool();
        }
        
        // Placeholder functions for other features
        function inspectOfflineData() {
            log('info', 'Inspect offline data - feature coming soon');
        }
        
        function clearCorruptedData() {
            log('info', 'Clear corrupted data - feature coming soon');
        }
        
        function resetAllOfflineData() {
            log('info', 'Reset all offline data - feature coming soon');
        }
        
        function testManualSync() {
            log('info', 'Test manual sync - feature coming soon');
        }
        
        function simulateOfflineData() {
            log('info', 'Simulate offline data - feature coming soon');
        }
        
        function exportLogs() {
            const logData = logs.join('\n');
            const blob = new Blob([logData], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `pwa-sync-logs-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            log('info', 'Debug logs exported');
        }
    </script>
</body>
</html>