# Offline Mode Maintenance Guide

## Overview

This guide provides comprehensive maintenance instructions for the Hauling QR Trip System's offline functionality, specifically for the DriverConnect (`/driver-connect`) and TripScanner (`/trip-scanner`) pages. The offline mode enables drivers to continue working during network outages and synchronize data when connectivity is restored.

## Architecture Overview

### Core Components

#### 1. Service Worker (`client/public/sw.js`)
- **Purpose**: Handles offline caching, API request interception, and background sync
- **Key Features**:
  - Selective API interception (only for PWA pages when offline)
  - Dynamic chunk caching with asset manifest integration
  - Background sync coordination with main thread
  - Network transition handling

#### 2. PWA Status Hook (`client/src/hooks/usePWAStatus.js`)
- **Purpose**: Provides centralized offline status and sync management
- **Key Features**:
  - Debounced network detection (300ms minimum delay)
  - Stable sync triggers using useCallback
  - Sync status management with 5-second intervals
  - Component lifecycle stability patterns

#### 3. Offline Services
- **`offlineDB.js`**: Core IndexedDB management with atomic operations
- **`driverConnectOffline.js`**: Driver connection offline storage
- **`tripScannerOffline.js`**: Trip scanner offline storage with 4-phase workflow
- **`backgroundSync.js`**: Centralized sync coordination with conflict resolution

#### 4. PWA Pages
- **`DriverConnect.js`**: Authentication-free driver connection with offline support
- **`TripScanner.js`**: QR scanning with offline 4-phase workflow integrity

## Maintenance Procedures

### 1. Service Worker Updates

#### When to Update
- Adding new offline-capable pages
- Modifying cache strategies
- Updating background sync logic
- Changing API interception patterns

#### Update Process
```javascript
// 1. Update cache version in sw.js
const CACHE_NAME = 'hauling-qr-v1.5.0'; // Increment version

// 2. Add new routes to PWA route handling
const pwaRoutes = ['/driver-connect', '/trip-scanner', '/new-offline-page'];

// 3. Test service worker registration
// Check browser dev tools > Application > Service Workers
```

#### Critical Patterns to Maintain
```javascript
// Always use skipWaiting() for immediate activation
self.skipWaiting();

// Always claim clients immediately
self.clients.claim();

// Only intercept API requests from PWA pages when offline
if (isPWAPage && !navigator.onLine) {
  event.respondWith(handleApiRequest(request));
}
```

### 2. PWA Status Hook Maintenance

#### Stability Requirements
```javascript
// Always use useCallback for event handlers
const triggerSync = useCallback(() => {
  // Sync logic here
}, [dependencies]);

// Always use debounced network detection
const debouncedNetworkCheck = useMemo(() => 
  debounce(() => {
    setIsOffline(!navigator.onLine);
  }, 300), []
);

// Always include proper cleanup
useEffect(() => {
  const cleanup = () => {
    // Cleanup logic
  };
  return cleanup;
}, []);
```

#### Common Issues and Solutions
```javascript
// ❌ WRONG: Object dependencies cause infinite loops
useEffect(() => {
  // Logic here
}, [{ some: 'object' }]);

// ✅ CORRECT: Use primitive dependencies or useMemo
const memoizedObject = useMemo(() => ({ some: 'object' }), []);
useEffect(() => {
  // Logic here
}, [memoizedObject]);
```

### 3. Offline Services Maintenance

#### IndexedDB Schema Updates
```javascript
// Always maintain backward compatibility
const DB_VERSION = 3; // Increment for schema changes

// Handle version upgrades gracefully
db.onupgradeneeded = (event) => {
  const db = event.target.result;
  
  // Check if object store exists before creating
  if (!db.objectStoreNames.contains('trips')) {
    const tripStore = db.createObjectStore('trips', { keyPath: 'id' });
    tripStore.createIndex('status', 'status', { unique: false });
  }
};
```

#### Sync Conflict Resolution
```javascript
// Always use atomic operations for conflict resolution
async function resolveConflict(localData, serverData) {
  // Use server timestamp as source of truth
  if (serverData.updated_at > localData.updated_at) {
    return serverData;
  }
  
  // Merge non-conflicting fields
  return {
    ...localData,
    ...serverData,
    // Preserve local changes for specific fields
    local_notes: localData.local_notes
  };
}
```

### 4. Component Stability Maintenance

#### React Patterns to Follow
```javascript
// ✅ CORRECT: Stable component patterns
const MyComponent = () => {
  // Use useCallback for event handlers
  const handleClick = useCallback(() => {
    // Handler logic
  }, [dependencies]);
  
  // Use useMemo for computed values
  const computedValue = useMemo(() => {
    return expensiveComputation(data);
  }, [data]);
  
  // Use proper dependency arrays
  useEffect(() => {
    // Effect logic
  }, [specificDependency]); // Not empty array unless truly no dependencies
  
  return <div onClick={handleClick}>{computedValue}</div>;
};
```

#### Patterns to Avoid
```javascript
// ❌ WRONG: These patterns cause refresh loops
useEffect(() => {
  // Logic here
}); // Missing dependency array

useEffect(() => {
  setData({ ...data, newField: 'value' });
}, [data]); // Object dependency causes infinite loop

const handleClick = () => {
  // Handler logic without useCallback
}; // Creates new function on every render
```

## Troubleshooting Guide

### 1. Infinite Refresh Loops

#### Symptoms
- Page continuously reloads
- Console shows repeated component mount/unmount cycles
- High CPU usage in browser

#### Diagnosis
```javascript
// Add temporary logging to identify the cause
useEffect(() => {
  console.log('Effect triggered with dependencies:', dependencies);
  // Effect logic
}, [dependencies]);
```

#### Solutions
```javascript
// Fix 1: Stabilize object dependencies
const stableObject = useMemo(() => ({ key: value }), [value]);

// Fix 2: Use primitive dependencies
const { field1, field2 } = complexObject;
useEffect(() => {
  // Logic
}, [field1, field2]); // Instead of [complexObject]

// Fix 3: Add proper cleanup
useEffect(() => {
  const timer = setInterval(() => {
    // Logic
  }, 1000);
  
  return () => clearInterval(timer); // Always cleanup
}, []);
```

### 2. Service Worker Not Updating

#### Symptoms
- Changes to sw.js not reflected in browser
- Old cache versions persisting
- API requests not being intercepted correctly

#### Solutions
```javascript
// 1. Force service worker update
navigator.serviceWorker.register('/sw.js', { updateViaCache: 'none' });

// 2. Clear all caches programmatically
caches.keys().then(names => {
  names.forEach(name => caches.delete(name));
});

// 3. Update cache version numbers
const CACHE_NAME = 'hauling-qr-v1.X.X'; // Always increment
```

### 3. Offline Data Not Syncing

#### Symptoms
- Manual sync button not working
- Background sync not triggering
- Data loss during network transitions

#### Diagnosis
```javascript
// Check IndexedDB data
const checkOfflineData = async () => {
  const db = await openDB();
  const transaction = db.transaction(['trips'], 'readonly');
  const store = transaction.objectStore('trips');
  const data = await store.getAll();
  console.log('Offline data:', data);
};
```

#### Solutions
```javascript
// 1. Verify sync service registration
if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
  navigator.serviceWorker.ready.then(registration => {
    registration.sync.register('comprehensive-sync');
  });
}

// 2. Check network status handling
const handleNetworkChange = useCallback(() => {
  if (navigator.onLine && hasOfflineData) {
    triggerSync();
  }
}, [hasOfflineData, triggerSync]);
```

### 4. Authentication Bypass Issues

#### Symptoms
- Login screens appearing on offline pages
- Authentication redirects during offline mode
- Protected routes not accessible offline

#### Solutions
```javascript
// Ensure proper offline detection in components
const { isOffline } = usePWAStatus();

// Bypass authentication for offline mode
if (isOffline) {
  // Skip authentication checks
  return <OfflineComponent />;
}

// Normal authentication flow for online mode
return <AuthenticatedComponent />;
```

## Performance Monitoring

### Key Metrics to Monitor

#### 1. Service Worker Performance
```javascript
// Monitor cache hit rates
let cacheHits = 0;
let cacheMisses = 0;

self.addEventListener('fetch', (event) => {
  caches.match(event.request).then(response => {
    if (response) {
      cacheHits++;
    } else {
      cacheMisses++;
    }
  });
});
```

#### 2. Sync Performance
```javascript
// Monitor sync success rates
const syncMetrics = {
  attempts: 0,
  successes: 0,
  failures: 0,
  averageTime: 0
};

const trackSyncPerformance = (startTime, success) => {
  syncMetrics.attempts++;
  const duration = Date.now() - startTime;
  
  if (success) {
    syncMetrics.successes++;
  } else {
    syncMetrics.failures++;
  }
  
  syncMetrics.averageTime = 
    (syncMetrics.averageTime * (syncMetrics.attempts - 1) + duration) / 
    syncMetrics.attempts;
};
```

#### 3. Memory Usage
```javascript
// Monitor IndexedDB storage usage
const checkStorageUsage = async () => {
  if ('storage' in navigator && 'estimate' in navigator.storage) {
    const estimate = await navigator.storage.estimate();
    console.log('Storage usage:', estimate.usage, '/', estimate.quota);
  }
};
```

## Security Considerations

### 1. Authentication Bypass Scope
- **Limited Scope**: Only applies to `/driver-connect` and `/trip-scanner` pages
- **Protected Routes**: All other routes maintain full authentication requirements
- **Data Access**: Offline mode only accesses driver's own data, not system-wide data

### 2. Data Validation
```javascript
// Always validate offline data before sync
const validateOfflineData = (data) => {
  // Check required fields
  if (!data.driver_id || !data.timestamp) {
    throw new Error('Invalid offline data structure');
  }
  
  // Validate data types
  if (typeof data.driver_id !== 'string') {
    throw new Error('Invalid driver_id type');
  }
  
  // Check data freshness (prevent stale data sync)
  const maxAge = 24 * 60 * 60 * 1000; // 24 hours
  if (Date.now() - data.timestamp > maxAge) {
    throw new Error('Offline data too old');
  }
  
  return true;
};
```

### 3. Audit Trail Maintenance
```javascript
// Always log offline operations for audit
const logOfflineOperation = (operation, data) => {
  const auditEntry = {
    timestamp: Date.now(),
    operation,
    data: sanitizeForAudit(data),
    offline: true,
    user_agent: navigator.userAgent
  };
  
  // Store in separate audit table
  storeAuditEntry(auditEntry);
};
```

## Future Enhancements

### 1. Additional Offline Pages
To add new pages to offline mode:

```javascript
// 1. Update service worker PWA routes
const pwaRoutes = ['/driver-connect', '/trip-scanner', '/new-page'];

// 2. Create offline service for new page
// client/src/services/newPageOffline.js

// 3. Integrate with PWA status hook
const { isOffline, syncStatus, triggerSync } = usePWAStatus();

// 4. Update background sync handling
if (event.tag === 'new-page-sync') {
  event.waitUntil(syncOfflineData('new-page'));
}
```

### 2. Enhanced Conflict Resolution
```javascript
// Implement field-level conflict resolution
const resolveFieldConflicts = (local, server, conflictRules) => {
  const resolved = {};
  
  Object.keys(local).forEach(field => {
    const rule = conflictRules[field] || 'server-wins';
    
    switch (rule) {
      case 'server-wins':
        resolved[field] = server[field];
        break;
      case 'client-wins':
        resolved[field] = local[field];
        break;
      case 'merge':
        resolved[field] = mergeField(local[field], server[field]);
        break;
    }
  });
  
  return resolved;
};
```

### 3. Progressive Sync
```javascript
// Implement priority-based sync
const syncByPriority = async () => {
  const priorities = ['critical', 'high', 'medium', 'low'];
  
  for (const priority of priorities) {
    const data = await getOfflineDataByPriority(priority);
    if (data.length > 0) {
      await syncData(data);
      // Break if network becomes unavailable
      if (!navigator.onLine) break;
    }
  }
};
```

## Conclusion

The offline functionality is fully implemented and production-ready. This maintenance guide provides the necessary information to:

- Maintain system stability and performance
- Troubleshoot common issues
- Implement future enhancements
- Monitor system health
- Ensure security compliance

Regular monitoring of the key metrics and following the established patterns will ensure continued reliable operation of the offline functionality.