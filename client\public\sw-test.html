<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Service Worker Test - Hauling QR</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 Service Worker Test Page</h1>
    
    <div class="test-section">
        <h2>Service Worker Status</h2>
        <div id="swStatus" class="status info">Checking...</div>
        <button onclick="checkServiceWorker()">🔄 Refresh Status</button>
        <button onclick="unregisterSW()">❌ Unregister SW</button>
        <button onclick="registerSW()">✅ Register SW</button>
    </div>

    <div class="test-section">
        <h2>Cache Status</h2>
        <div id="cacheStatus" class="status info">Checking...</div>
        <button onclick="checkCaches()">🔄 Check Caches</button>
        <button onclick="clearCaches()">🗑️ Clear All Caches</button>
    </div>

    <div class="test-section">
        <h2>PWA Detection</h2>
        <div id="pwaStatus" class="status info">Checking...</div>
        <button onclick="checkPWAStatus()">🔄 Check PWA Status</button>
    </div>

    <div class="test-section">
        <h2>Offline Test</h2>
        <div id="offlineStatus" class="status info">Ready to test</div>
        <button onclick="testOfflineNavigation()">📱 Test Offline Navigation</button>
        <p><strong>Instructions:</strong> Click the button above, then turn off your internet and try to navigate to /driver-connect</p>
    </div>

    <div class="test-section">
        <h2>Debug Log</h2>
        <pre id="debugLog">Ready...\n</pre>
        <button onclick="clearLog()">🗑️ Clear Log</button>
    </div>

    <script>
        function log(message) {
            const logElement = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        async function checkServiceWorker() {
            const statusElement = document.getElementById('swStatus');
            
            if (!('serviceWorker' in navigator)) {
                statusElement.className = 'status error';
                statusElement.textContent = '❌ Service Worker not supported';
                log('Service Worker not supported in this browser');
                return;
            }

            try {
                const registration = await navigator.serviceWorker.getRegistration();
                if (registration) {
                    statusElement.className = 'status success';
                    statusElement.innerHTML = `
                        ✅ Service Worker registered<br>
                        State: ${registration.active ? registration.active.state : 'No active worker'}<br>
                        Scope: ${registration.scope}<br>
                        Update found: ${registration.waiting ? 'Yes' : 'No'}
                    `;
                    log(`Service Worker registered: ${registration.scope}`);
                } else {
                    statusElement.className = 'status error';
                    statusElement.textContent = '❌ Service Worker not registered';
                    log('Service Worker not registered');
                }
            } catch (error) {
                statusElement.className = 'status error';
                statusElement.textContent = `❌ Error checking SW: ${error.message}`;
                log(`Error checking Service Worker: ${error.message}`);
            }
        }

        async function registerSW() {
            try {
                const registration = await navigator.serviceWorker.register('/sw.js');
                log(`Service Worker registered: ${registration.scope}`);
                await checkServiceWorker();
            } catch (error) {
                log(`Service Worker registration failed: ${error.message}`);
                await checkServiceWorker();
            }
        }

        async function unregisterSW() {
            try {
                const registration = await navigator.serviceWorker.getRegistration();
                if (registration) {
                    await registration.unregister();
                    log('Service Worker unregistered');
                } else {
                    log('No Service Worker to unregister');
                }
                await checkServiceWorker();
            } catch (error) {
                log(`Error unregistering Service Worker: ${error.message}`);
            }
        }

        async function checkCaches() {
            const statusElement = document.getElementById('cacheStatus');
            
            try {
                const cacheNames = await caches.keys();
                if (cacheNames.length > 0) {
                    statusElement.className = 'status success';
                    let cacheInfo = `✅ Found ${cacheNames.length} caches:<br>`;
                    
                    for (const cacheName of cacheNames) {
                        const cache = await caches.open(cacheName);
                        const keys = await cache.keys();
                        cacheInfo += `• ${cacheName}: ${keys.length} items<br>`;
                    }
                    
                    statusElement.innerHTML = cacheInfo;
                    log(`Found ${cacheNames.length} caches: ${cacheNames.join(', ')}`);
                } else {
                    statusElement.className = 'status error';
                    statusElement.textContent = '❌ No caches found';
                    log('No caches found');
                }
            } catch (error) {
                statusElement.className = 'status error';
                statusElement.textContent = `❌ Error checking caches: ${error.message}`;
                log(`Error checking caches: ${error.message}`);
            }
        }

        async function clearCaches() {
            try {
                const cacheNames = await caches.keys();
                await Promise.all(cacheNames.map(name => caches.delete(name)));
                log(`Cleared ${cacheNames.length} caches`);
                await checkCaches();
            } catch (error) {
                log(`Error clearing caches: ${error.message}`);
            }
        }

        function checkPWAStatus() {
            const statusElement = document.getElementById('pwaStatus');
            
            const isPWAMode = window.matchMedia('(display-mode: standalone)').matches || 
                             window.navigator.standalone === true;
            
            const isInstallable = window.deferredPrompt !== undefined;
            
            statusElement.className = 'status info';
            statusElement.innerHTML = `
                PWA Mode: ${isPWAMode ? '✅ Yes' : '❌ No'}<br>
                Installable: ${isInstallable ? '✅ Yes' : '❌ No'}<br>
                Display Mode: ${window.matchMedia('(display-mode: standalone)').matches ? 'Standalone' : 'Browser'}<br>
                User Agent: ${navigator.userAgent.includes('Mobile') ? 'Mobile' : 'Desktop'}
            `;
            
            log(`PWA Status - Mode: ${isPWAMode}, Installable: ${isInstallable}`);
        }

        function testOfflineNavigation() {
            const statusElement = document.getElementById('offlineStatus');
            statusElement.className = 'status info';
            statusElement.innerHTML = `
                📱 Test prepared!<br>
                1. Turn off your internet connection<br>
                2. Click this link: <a href="/driver-connect" target="_blank">Open Driver Connect</a><br>
                3. It should load from cache, not show "site can't be reached"
            `;
            log('Offline navigation test prepared');
        }

        function clearLog() {
            document.getElementById('debugLog').textContent = 'Log cleared...\n';
        }

        // Initialize
        window.addEventListener('load', async () => {
            log('Page loaded, running initial checks...');
            await checkServiceWorker();
            await checkCaches();
            checkPWAStatus();
        });

        // Listen for service worker events
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('message', (event) => {
                log(`SW Message: ${JSON.stringify(event.data)}`);
            });
        }
    </script>
</body>
</html>